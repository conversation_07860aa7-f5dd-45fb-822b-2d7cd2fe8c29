import './NewAuthBase.min.css'

import { useNavigation } from "react-router-dom";
import NewAuthPageNavbar from "../../components/NewAuthPageNavbar/NewAuthPageNavbar";
import TransitionOutlet from "../../components/TransitionOutlet/TransitionOutlet";
import ProgressBar from "../../components/ProgressBar/ProgressBar";

export default function NewAuthBase() {
	const navigation = useNavigation();

	return (
		<div className={"authbase-container"}>
			<ProgressBar show={navigation.state === "loading"} />
			{/* ----------------------- NAVBAR ----------------------- */}
			<NewAuthPageNavbar />

			{/* ----------------------- MAIN BODY ----------------------- */}
			<div className="container">
				<TransitionOutlet />
			</div>
		</div>
	)
}
