import './SignupPlanSelection.min.css';

import { useMutation, useQuery } from "@tanstack/react-query";
import { useEffect, useRef, useState } from "react";
import ReactGA from "react-ga4";
import { useNavigate } from "react-router-dom";
import checkCircle from '../../assets/images/check-mark-circle.webp';
import AbunLoader from "../../components/AbunLoader/AbunLoader";
import AbunModal from '../../components/AbunModal/AbunModal';
import ErrorAlert from '../../components/ErrorAlert/ErrorAlert';
import Icon from "../../components/Icon/Icon";
import { APIError, generateV2ArticleMutation, getAllPlanDataQuery, makeApiRequest, purchasePlanMutation, useFreePlanMutation } from "../../utils/api";
import { pageURL } from "../routes";
import AbunButton from '../../components/AbunButton/AbunButton';

interface PlanCardProps {
	planName: string
	priceID: string
	amount: number
	currencyCode: "inr" | "usd"
	description: string
	articleCount: number
	websiteCount: number
	competitorsCount: number
	popular: boolean

	buttonDisabled: boolean
	checkoutFunction: (planName: string, priceID: string) => void;
}

interface PlanFeature {
	label: string
	value: string | number
}

interface PlanTool {
	label: string
	value?: string | number
	comingSoon?: boolean
}

interface PlanData {
	id: string
	name: string
	metadata: PlanMetadata
	price_id: string
	price_amount: number
	currency_code: "inr" | "usd"
	articles: number
	credits: number
	sites: number
	annual_plan: boolean
	features: Array<PlanFeature>
	tools: Array<PlanTool>
}

interface PlanMetadata {
	description: string
	max_articles: number
	max_titles: number
	max_keywords: number
	position: number
	websites: number
	popular: boolean
	show: boolean
}

// ********************************************************************
// -------------------------- MAIN COMPONENT --------------------------
// ********************************************************************

export default function SignupPlanSelection() {
	const navigate = useNavigate();

	// -------------------- NON-STATE CONSTANTS --------------------
	const productsToShow = ["seed", "starter", "growth", "growth max"];

	// -------------------- STATES --------------------
	const [planCardsData, setPlanCardsData] = useState<Array<PlanData>>([]);
	const [planPurchaseUnderway, setPlanPurchaseUnderway] = useState(false);
	const [Error, setError] = useState('');
	const [modalText, setModalText] = useState("");
	const [requestModalActive, setRequestModalActive] = useState(false);
	const failAlertRef = useRef<any>(null);
	const [isVerified, setVerified] = useState(false);
	const [articleUID, setArticleUid] = useState('');
	const [activePricingTab, setActivePricingTab] = useState("monthly");


	// -------------------- QUERIES --------------------
	const allPlanData = useQuery(getAllPlanDataQuery());

	// -------------------- MUTATIONS --------------------
	const purchasePlan = useMutation(purchasePlanMutation);
	const useFreePlan = useMutation(useFreePlanMutation);

	// -------------------- REFS --------------------
	const errorAlertRef = useRef<any>(null);

	// -------------------- EFFECTS --------------------
	useEffect(() => {
		document.title = "Select Plan | Abun"
		ReactGA.gtag('event', 'conversion', { 'send_to': 'AW-11453495091/Agb3CKHoj4MZELPuudUq' });
	}, []);

	useEffect(() => {
		if (allPlanData.data) {
			setPlanCardsData((allPlanData.data as any)['data'] as Array<PlanData>);
		}
	}, [allPlanData.data]);

	// =======================================================
	// ---------------------- MAIN CODE ----------------------
	// =======================================================
	function checkoutHandler(planName: string, priceID: string) {
		if (planName.toLowerCase() === 'trial') {
			freePlanCheckout(priceID);
		} else {
			paidCheckout(priceID);
		}
	}

	function paidCheckout(priceID: string) {
		setPlanPurchaseUnderway(true);

		let successURL = process.env.REACT_APP_STRIPE_CHECKOUT_SUCCESS_URL;
		let cancelURL = process.env.REACT_APP_STRIPE_CHECKOUT_CANCEL_URL;

		if (successURL && cancelURL && priceID) {
			purchasePlan.mutate({
				priceID: priceID,
				successURL: successURL,
				cancelURL: cancelURL
			}, {
				onSuccess: (response) => {
					window.location.href = response['data']['checkout_url'];
				},
				onError: (error) => {
					setPlanPurchaseUnderway(false);
					console.error(`Stripe Checkout Failed: ${error}`);
				}
			});
		} else {
			setPlanPurchaseUnderway(false);
			errorAlertRef.current?.show("Oops! Something went wrong :( Please try again later or contact us for support.");
		}
	}

	// Function to format INR prices in k format
	function formatINRPrice(priceInPaise: number): string {
		const priceInRupees = priceInPaise / 100;
		if (priceInRupees >= 1000) {
			const priceInK = priceInRupees / 1000;
			// Format to remove unnecessary decimal places
			if (priceInK % 1 === 0) {
				return `${priceInK}k`;
			} else {
				return `${priceInK.toFixed(2).replace(/\.?0+$/, '')}k`;
			}
		}
		return priceInRupees.toString();
	}

	// Clean pricing display function
	function formatPlanPrice(plan: PlanData, isDiscounted: boolean = false): string {
		let displayAmount = plan.price_amount;

		// For annual plans, divide by 12 to show per-month cost
		if (plan.annual_plan) {
			displayAmount = Math.round(displayAmount / 12);
		}

		// Apply discount if specified (50% off)
		if (isDiscounted) {
			displayAmount = Math.round(displayAmount * 0.5);
		}

		// Format based on currency
		if (plan.currency_code === 'inr') {
			return `₹${formatINRPrice(displayAmount)}`;
		} else {
			const dollarAmount = Math.round(displayAmount / 100);
			return `$${dollarAmount}`;
		}
	}

	function freePlanCheckout(priceID: string) {
		setPlanPurchaseUnderway(true);

		useFreePlan.mutate(priceID, {
			onSuccess: () => {
				if (keyword) {
					// Call generateNewArticle only if keyword exists
					generateNewArticle();

					// Remove keyword from local storage after generating the article
					localStorage.removeItem('keyword');
				} else {
					navigate(pageURL['createArticle']);
				}

			},
			onError: (error) => {
				console.error(error)
				setPlanPurchaseUnderway(false);
				errorAlertRef.current?.show("Oops! Something went wrong :( Please try again later or contact us for support.");
			}
		});
	}

	//  <!-----  Generate Article  ---!>
	// Retrieve stored data from local storage
	const keyword = localStorage.getItem('keyword');
	const title = localStorage.getItem('selectedTitle');
	const creative_titles = JSON.parse(localStorage.getItem('creativeTitles') || '[]');
	const serp_titles = JSON.parse(localStorage.getItem('serpTitles') || '[]');

	const generateNewArticle = async () => {
		if (!title || !keyword) {
			return;
		}
		setError('');
		try {
			const response = await makeApiRequest(
				'/api/frontend/new-article-data/',
				'post',
				{
					keyword: keyword,
					title: title,
					serp_titles: serp_titles,
					creative_titles: creative_titles,
				}
			);

			// Assuming the response structure contains the necessary fields
			const data = response.data;
			if (data.success) {
				setArticleUid(data.article_uid);
				generateArticleHandler(data.article_uid);
			} else {
				setError(data.error || 'Failed to generate article.');
			}
		} catch (err) {
			if (err instanceof APIError) {
				if (err.statusCode === 401) {
					setError('Unauthorized. Please log in again.');
				} else {
					setError('An error occurred while generating the article: ' + (err.responseData.message || 'Unknown error'));
				}
			} else {
				setError('An unexpected error occurred: ' + ('Unknown error'));
			}
		}
	}
	//Logic to Generate Article
	const generateArticle = useMutation(generateV2ArticleMutation);
	function generateArticleHandler(articleUID: string, articleContext?: string) {
		setModalText("Processing request. Please wait...");
		setRequestModalActive(true);
		generateArticle.mutate({ articleUID, context: articleContext ?? "" }, {
			onSuccess: (data) => {
				setRequestModalActive(false);
				let responseData = (data as any)["data"];

				if (responseData["status"] === "sent_for_processing") {
					navigate(`/articles/edit/${articleUID}/`);
				} else if (responseData["status"] === "rejected") {
					if (responseData["reason"] === "max_limit_reached") {
						failAlertRef.current?.show("Article generation request failed. " +
							"You have reached your max article generation limit for the month.");
					} else {
						failAlertRef.current?.show(`Article generation request failed. Error ID: ${responseData["reason"]}`);
					}
				} else {
					failAlertRef.current?.show(
						`Article generation request returned unknown status ${responseData["status"]}. Please contact us if there's any issue.`
					);
				}
			},
			onError: (error: Error) => {
				console.error(error);
				setRequestModalActive(false);
				failAlertRef.current?.show(`Article generation request failed. Please try again later`)
				setTimeout(() => {
					failAlertRef.current?.close();
				}, 5000);
			}
		});
	}

	return (
		<div className={"signup-plan-selection-container font-primary"}>

			{/* ----------------------- MAIN BODY ----------------------- */}
			<div className={"columns page-padding"}>
				<div className={"column"}>
					{/* <div className="header">
						<a href="https://abun.com" className="header-logo">
							<img src="data:image/png;base64,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" width="128" height="auto" alt="Abun Logo" />
						</a>
						<div className={"header-right-content"}>
							<h2 className={"header-text"}>Choose Plan to Proceed</h2>
							<NavLink to={`/logout`} className={"logout"}>
								Logout
							</NavLink>
						</div>
					</div> */}
					<div className={"plan-selection-content mt-5 w-100"}>
						{/* <div className={"offer-card"}>
							<h2 className="offer">Limited Time Offer: 50% OFF for First Month. No Coupon Required.</h2>
						</div> */}
						<div className="has-text-centered subscription-page-header">
							<h3 className="mb-5 has-text-weight-bold is-size-1">Choose Plan to Proceed.</h3>

							<div className="tabs is-centered">
								<ul>
									<li className={activePricingTab === "monthly" ? "is-active" : ""}>
										<a onClick={() => setActivePricingTab("monthly")}>Monthly</a>
									</li>
									<li className={activePricingTab === "annual" ? "is-active" : ""}>
										<a onClick={() => setActivePricingTab("annual")}>Annual <span className="ml-1 has-text-weight-light"> (Limited Time Offer 6 Months Free)</span></a>
									</li>
								</ul>
							</div>

							<h2 className="my-5 has-text-weight-normal has-text-primary is-size-5">
								{activePricingTab === "monthly" ? "Limited Time Offer: 50% OFF for First Month. No Coupon Code Required."
									: "Pay for 6 Months, Get 6 Months for Free. No Coupon Code Required."
								}
							</h2>

						</div>
						{/* **************************** PLAN CARDS **************************** */}
						{allPlanData.isLoading ? <p className={"has-text-centered"} style={{ fontSize: "1.5em" }}>
							<AbunLoader show={allPlanData.isLoading} height="20vh" />
						</p> :
							<>
								<div className={"plan-cards-container-new-pricing"}>
									{planCardsData
										.filter(plan => {
											// Filter plans based on active pricing tab and annual_plan field
											if (activePricingTab === "annual") {
												return plan.annual_plan === true;
											} else {
												return plan.annual_plan === false;
											}
										})
										.map(plan => {
											if (productsToShow.includes(plan.name.toLowerCase())) {
												return (
													<div
														className={plan.name === "Growth Max" ? "plan-card popular" : "plan-card"}
														key={plan.id}
														id={plan.name.toLowerCase()}>

														<div className="is-flex is-flex-direction-column" style={{ gap: '10px', height: 'auto' }}>

															{/* ---------- PLAN NAME ----------- */}
															<h5 className={"plan-name"}>
																{plan.name}
															</h5>

															{/* ----------- PLAN CUTTING PRICE (Original Price) ------------- */}
															<div className={"is-flex is-align-items-center pricing-gap"}>
																<p className={"cancel-pricing"}>
																	{formatPlanPrice(plan, false)}
																</p>
															</div>

														</div>

														{/* ------------  Plan Pricing (Discounted) ------------  */}
														<div className={"is-flex is-align-items-center pricing-gap"}>
															<p className={"pricing"}>
																{formatPlanPrice(plan, true)}
																<span className={"pricing-suffix"}> {activePricingTab === "monthly" ? "first month" : "per month"}</span>
															</p>
														</div>

														{/* ------- Show when User is on ANNUAL TAB ---------- */}
														{activePricingTab === "annual" &&
															<span style={{ marginTop: '-22px', marginBottom: '14px' }}>Billed Yearly</span>
														}

														<div className={"plan-details"}>
															<PricingCard plan={plan} />

															{/* ---------- Plans Button ----------- */}
															<AbunButton type={"primary"}
																disabled={planPurchaseUnderway}
																clickHandler={() => {
																	checkoutHandler(plan.name, plan.price_id)
																}}
																className="mt-6 mb-2 button is-primary is-outlined plan-purchase-button">
																PROCEED
															</AbunButton>
														</div>

													</div>
												)
											} else {
												return <></>
											}
										})}
								</div>
							</>}
					</div>
				</div>
			</div>
			<div className={"signup-plan-selection--notification-section"}>
				{planPurchaseUnderway && <div className="notification is-success position-bottom has-text-weight-bold">
					Setting up your account with selected plan. Please wait...&nbsp;&nbsp;
					<Icon iconName={"spinner"} additionalClasses={["icon-white"]} />
				</div>}
				<ErrorAlert ref={errorAlertRef} />
			</div>
			{/* ------------------------------ ONGOING REQUEST MODAL ------------------------------ */}
			<AbunModal active={requestModalActive}
				headerText={""}
				closeable={false}
				hideModal={() => {
					setRequestModalActive(false)
				}}>
				<div className={"loadingData w-100 is-flex is-justify-content-center is-align-items-center"}>
					<AbunLoader show={requestModalActive} height="20vh" />
				</div>
				<p className={"is-size-4 has-text-centered mb-4"}>{modalText}</p>
			</AbunModal>

			<ErrorAlert ref={failAlertRef} />
		</div>
	)
}


const Tag = ({ children }: { children: React.ReactNode }) => (
	<span className="tag is-rounded has-text-black ml-2" style={{ background: '#FAC44B', fontSize: '14px', lineHeight: '1.2' }}>{children}</span>
);

const ComingSoon = () => (
	<span
		className="tag is-light is-rounded ml-2"
		style={{ fontSize: "14px", color: '#686868', background: '#F1F1F1' }}
	>
		Coming Soon
	</span>
);

const PricingCard = ({ plan }: { plan: PlanData }) => {

	return (
		<div style={{ color: '#000c60' }}>
			<div className="mt-4">
				<p className="mb-3 plan-article-gradient"><b>{plan.articles}</b> AI Articles</p>
				<p className="mb-3 plan-article-gradient"><b>{plan.credits}</b> Keyword Research Credits</p>
				<p className="mb-3">
					{typeof plan.sites === "number" ? (
						<span className="plan-article-gradient"><b>{plan.sites}</b> Sites</span>
					) : (
						<span className="is-flex is-align-items-center is-justify-content-space-between">
							<b className="plan-article-gradient">Sites</b> <Tag>Unlimited</Tag>
						</span>
					)}
				</p>
				<p className="mb-3 plan-article-gradient"><b>Access to All Tools</b></p>
				<p className="mb-3 plan-article-gradient"><b>All Integrations</b></p>
			</div>

			<hr />

			<ul>
				{plan.features.map((feature: PlanFeature) => (
					<li key={feature.label} className={` ${plan.name === "Growth Max" ? "mb-2" : "mb-3 mr-3"}`}>
						{feature.value === "Unlimited" ? (
							<span className="is-flex is-align-items-center is-justify-content-space-between">
								{feature.label}

								<Tag>Unlimited</Tag>
							</span>
						) : (
							<>
								<b>{feature.value}</b>&nbsp;{feature.label}
							</>
						)}
					</li>
				))}
			</ul>


			<ul>
				{plan.tools.map((tool: PlanTool) => (
					<li key={tool.label} className={`mb-3 ${plan.name !== "Growth Max" ? "mr-3" : ""}`}>
						{!tool.comingSoon ? (
							<span className="is-flex is-align-items-center">
								<img src={checkCircle} alt="Check Mark" style={{ width: '24px', marginRight: '0.95rem' }} />
								{tool.value && <b className="mr-1">{tool.value}</b>}
								{tool.label}
							</span>
						) : (
							<span className="is-flex is-align-items-center is-justify-content-space-between">
								<span>
									<img src={checkCircle} alt="Check Mark" style={{ width: '24px', marginRight: '0.95rem' }} />
									{tool.label}
								</span>
								{tool.comingSoon && <ComingSoon />}
							</span>
						)}
					</li>
				))}
			</ul>
		</div>
	);
};