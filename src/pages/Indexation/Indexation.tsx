/* eslint-disable jsx-a11y/anchor-is-valid */
import './Indexation.min.css';
import { useEffect, useRef, useState } from "react";
import { useLoaderData, useSearchParams, Link, useNavigate } from "react-router-dom";
import { useUIState } from "../../hooks/UIStateContext";
import { useMutation } from "@tanstack/react-query";
import AllPages from "./AllPages";
import PagesSentForIndexing from "./PagesSentForIndexing";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import MessageBox from "../../components/MessageBox/MessageBox";
import { pageURL } from "../routes";
import { googleIntegrationMutation, saveSettingsMutation } from "../../utils/api";
import IndexationReport from "./IndexationReport";
import PostedAbunArticles from "./PostedAbunArticles";
import NotIndexedPages from "./NotIndexedPages";
import RejectedPages from "./RejectedPages";
import '../GSCAnalytics/GSCAnalytics.min.css';

type Tab = 'allPages' | 'indexed' | 'notIndexed' | 'inProgress' | 'rejected';

interface ServerData {
	user_verified: boolean
	has_active_website: boolean,
	integration_done: boolean,
	indexation_done: boolean,
	plan_name: string,
	current_active_website: string
	auto_indexing: boolean,
	indexed_allowed: boolean,
}

interface GoogleIntegrationResponse {
	success: boolean
	authorization_endpoint: string
	current_plan_name: string;
}

export default function Indexation() {
	// ------------------------- QUERY PARAMETERS -----------------------
	const [searchParams] = useSearchParams();
	const tab = searchParams.get("tab");

	// ----------------------------- LOADER -----------------------------
	const pageData: ServerData = useLoaderData() as ServerData;

	// ---------------------------- STATES ---------------------------
	const [currentTab, setCurrentTab] = useState<Tab | string>(
		tab || 'allPages'
	);
	const [selectedDomain, setSelectedDomain] = useState(pageData.current_active_website || "");
	const [GSCIntegrationProcessing, setGSCIntegrationProcessing] = useState(false);
	const { hamburgerActive } = useUIState();

	// ---------------------------- REFS ----------------------------
	const errorAlertRef = useRef<any>(null);
	const successAlertRef = useRef<any>(null);

	const navigate = useNavigate();
	const googleIntegrationMut = useMutation(googleIntegrationMutation);
	// ---------------------------- EFFECTS ---------------------------
	useEffect(() => {
		document.title = "Indexation | Abun";
	}, []);

	function handleBtn(tab) {
		navigate(`${pageURL['new-integration']}?integration=${tab}`);
	}

	function googleIntegration(integrationType: "google-search-console" | "google-analytics" | "google-drive") {
		errorAlertRef.current?.close();
		setGSCIntegrationProcessing(true);
		googleIntegrationMut.mutate(integrationType, {
			onSuccess: (data) => {
				const response: GoogleIntegrationResponse = data['data'];
				if (response.success) {
					successAlertRef.current?.show("New GSC Account Integrated successfully.");
					window.location.href = response.authorization_endpoint;
				} else {
					setGSCIntegrationProcessing(false);
					errorAlertRef.current?.show(
						"Oops! Something went wrong :( Please try " +
						"again later or contact us for further support."
					);
				}
			},
			onError: () => {
				setGSCIntegrationProcessing(false);
				errorAlertRef.current?.show(
					"Oops! Something went wrong :( Please try " +
					"again later or contact us for further support."
				);
			}
		})
	}

	function renderTab() {
		switch (currentTab) {
			case "allPages":
				return <AllPages />
			case "inProgress":
				return <PagesSentForIndexing />
			case "indexed":
				return <PostedAbunArticles />
			case "notIndexed":
				return <NotIndexedPages />
			case "rejected":
				return <RejectedPages />
			default:
				return <AllPages />
		}
	}

	if (pageData.plan_name === "Trial") {
		return (
			<MessageBox title={"No Active Website"}
				type={"warning"}
				className="no-website-message-box"
				blackBodyText={true}
				style={{ maxWidth: "100%" }}>
				This feature is available only on <b>paid</b> plans. Upgrade to a <Link
					to={pageURL['manageSubscription']}><b>paid plan now.</b></Link>
			</MessageBox>
		)
	} else if (!pageData.has_active_website) {
		return (
			<MessageBox title={"No Active GSC Account"}
				type={"warning"}
				className="no-website-message-box"
				blackBodyText={true}
				style={{ maxWidth: "100%" }}>
				You have not connected your GSC account with <b>Abun</b> yet.
				In order to use this feature you will have to connect your Google Search Console.
				<br />
				<br />
				The feature uses your Google Search Console to send indexing request to Google.
				<br />
				<br />
				Connect your <Link to={pageURL['connectWebsite']}><b>GSC account now.</b></Link>
			</MessageBox>
		)
	} else if (!pageData.integration_done) {
		return (
			<div className={"gsc-wrapper " + (pageData.plan_name === "Trial" ? "trial-user-overlay" : "")}>
				<div className="gsc-container">
					<div className={"is-flex is-justify-content-center is-align-items-center is-flex-direction-column  has-text-centered"}>
						<h2>Google Search Console Indexing for {selectedDomain}</h2>
						{pageData.integration_done ? "" : <p className={"is-size-6 mb-4"}>Get your pages indexed on Google faster with Fast Indexing</p>}
					</div>
					{
						!pageData.integration_done ?
							(
								<div className="gsc-box">
									<h2>Connect Google Search Console</h2>
									<p>Connect your GSC to instantly submit URLs for indexing and speed up visibility on search engines.</p>
									<div className="gsc-benefits">
										<p className="benefits-title">Benefits of connecting GSC:</p>
										<ul>
											<li>Notify Google instantly about new or updated pages.</li>
											<li>Improve chances of faster visibility in search results.</li>
											<li>Boost SEO by helping search engines discover fresh content.</li>
											<li>Reduce the typical delay in getting pages re-crawled</li>
										</ul>
									</div>
									<div className="gsc-privacy">
										<span className="privacy-icon">🔒</span>
										<div className="privacy-text">
											<span className="privacy-title">Your data stays private</span>
											<p>We only access the data you authorize. You can revoke access at any time.</p>
										</div>
									</div>
									<button className="gsc-connect-button" onClick={() => googleIntegration("google-search-console")}
										disabled={GSCIntegrationProcessing}>{GSCIntegrationProcessing ? "Connecting..." : "Connect Google Search Console"}</button>
								</div>
							) : <></>
					}
				</div>

				{!hamburgerActive && pageData.plan_name === "Trial" && (
					<div className="gsc-upgrade-modal">
						<h3>Upgrade to Unlock Fast Indexing.</h3>
						<p>Upgrade your plan to access all features.</p>
						<button onClick={() => navigate(pageURL["manageSubscription"])}>
							See Plans →
						</button>
					</div>
				)}
			</div>
		)
	} else if (!pageData.indexed_allowed) {
		return (
			<div className={"gsc-wrapper " + (pageData.plan_name === "Trial" ? "trial-user-overlay" : "")}>
				<div className="gsc-container">
					<div className="is-flex is-justify-content-center is-align-items-center is-flex-direction-column has-text-centered">
						<h2>Google Search Console Indexing for {selectedDomain}</h2>
						<p className="is-size-6 mb-4">
							Your Google Search Console is connected, but you don't have permission to index this domain.
						</p>
					</div>

					<div className="gsc-box">
						<h2>Indexing Not Allowed</h2>
						<p>Your Google Search Console is connected, but indexing is not permitted for <strong>{selectedDomain}</strong> using the Indexing.</p>
						<div className="gsc-benefits">
							<p className="benefits-title">What You Can Do:</p>
							<ul>
								<li>Make sure you're verified as an <strong>owner</strong>  of <strong>{selectedDomain}</strong> in Google Search Console.</li>
								<li>Only property owners (not delegated users) can use the Indexing for a domain.</li>
								<li>You can verify ownership in GSC settings, or contact your domain administrator for access.</li>
							</ul>
						</div>

						<div className="gsc-privacy">
							<span className="privacy-icon">🔒</span>
							<div className="privacy-text">
								<span className="privacy-title">Why this matters</span>
								<p>
									Google's Indexing is restricted to verified property owners for security and privacy. We never access or submit URLs without your permission.
								</p>
							</div>
						</div>
						<button className="gsc-connect-button" onClick={() => { handleBtn("GSC") }}
						>{"GSC Integration"}</button>
					</div>
				</div>
				{!hamburgerActive && pageData.plan_name === "Trial" && (
					<div className="gsc-upgrade-modal">
						<h3>Upgrade to Unlock Fast Indexing.</h3>
						<p>Upgrade your plan to access all features.</p>
						<button onClick={() => navigate(pageURL["manageSubscription"])}>
							See Plans →
						</button>
					</div>
				)}
			</div>
		);
	} else {
		return (
			<>
				{!pageData.indexation_done &&
					<MessageBox title={"Indexation Is Processing"}
						type={"warning"}
						className="no-website-message-box"
						blackBodyText={true}
						style={{ maxWidth: "100%" }}>
						We are currently in the process of indexing your website pages. We will email you once the process is completed!
					</MessageBox>
				}
				<h1 className="index-header">Fast Indexing Booster</h1>
				<p className="index-subtext mb-5">Keep your content fresh in Google’s eyes with instant index updates.</p>

				<IndexationReport errorRef={errorAlertRef} successRef={successAlertRef} />

				<div className="tabs custom-tabs w-100">
					<ul>
						{/* Indexation Report */}
						{/* <li className={currentTab === 'indexationReport' ? "is-active" : ""}
							onClick={() => setCurrentTab("indexationReport")}>
							<a>Indexation Report</a>
						</li> */}
						{/* All Pages */}
						<li className={currentTab === 'allPages' ? "is-active" : ""}
							onClick={() => setCurrentTab("allPages")}>
							<a>All Pages</a>
						</li>
						<li className={currentTab === 'indexed' ? "is-active" : ""}
							onClick={() => setCurrentTab("indexed")}>
							<a>Indexed</a>
						</li>
						{/* Posted Abun Pages */}
						<li className={currentTab === 'notIndexed' ? "is-active" : ""}
							onClick={() => setCurrentTab("notIndexed")}>
							<a>Not Indexed</a>
						</li>
						{/* Pages sent for indexing */}
						<li className={currentTab === 'inProgress' ? "is-active" : ""}
							onClick={() => setCurrentTab("inProgress")}>
							<a>In Progress</a>
						</li>
						<li className={currentTab === 'rejected' ? "is-active" : ""}
							onClick={() => setCurrentTab("rejected")}>
							<a>Rejected</a>
						</li>
					</ul>
				</div>
				<div className="tab-content w-100">
					{renderTab()}
				</div>
				<ErrorAlert ref={errorAlertRef} />
				<SuccessAlert ref={successAlertRef} />
			</>
		)
	}
}