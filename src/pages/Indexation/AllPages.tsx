import { Player } from "@lottiefiles/react-lottie-player";
import { useMutation, useQuery, MutationFunction  } from '@tanstack/react-query';
import { ColumnDef, createColumnHelper, RowData, RowModel, FilterFn } from "@tanstack/react-table";
import { useEffect, useRef, useState } from "react";
import { useLoaderData } from "react-router-dom";
import AbunModal from "../../components/AbunModal/AbunModal";
import AbunTable, { IndeterminateCheckbox } from "../../components/AbunTable/AbunTable";
import BookLoader from "../../components/BookLoader/BookLoader";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import GenericButton from "../../components/GenericButton/GenericButton";
import Icon from "../../components/Icon/Icon";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import { AxiosResponse } from "axios";
import { loadAllPageQuery, sendPageForIndexingMutation, saveSettingsMutation } from "../../utils/api";
import './Indexation.min.css';

type AllPages = {
    url: string,
    index: boolean
    last_crawled: string
    in_progress: boolean
    status: string
}

interface ServerData {
	user_verified: boolean
	has_active_website: boolean,
	integration_done: boolean,
	indexation_done: boolean,
	plan_name: string
}

export default function AllPages(){
    // ----------------------- NON STATE CONSTANTS -----------------------
	const pageSizes = [50, 100, 500, 1000];

    // ----------------------------- LOADER -----------------------------
	const pageData: ServerData = useLoaderData() as ServerData;

    // ----------------------- STATES -----------------------
    const [tableData, setTableData] = useState<Array<AllPages>>([]);
    const [searchText, setSearchText] = useState("");
    const [selectedPages, setSelectedPages] = useState<Array<string>>([]);
    const [requestModalActive, setRequestModalActive] = useState(false);    
    const [progressStatus, setProgressStatus] = useState<{ [url: string]: boolean }>({});
    const [modalText, setModalText] = useState("");

    // ----------------------- EFFECTS -----------------------
    const { isLoading, error, data, refetch } = useQuery(loadAllPageQuery());
    const sendPageForIndexing = useMutation(sendPageForIndexingMutation);    

    useEffect(() => {
		if (data) {
			setTableData((data as any)['data']['all_pages']);
		}
	}, [data]);

    useEffect(() => {
		if (window.location.search) {
			const searchParams = new URLSearchParams(window.location.search);
			const url = searchParams.get("url");
			setSearchText(url?.toLowerCase() || "");
		}
	}, []);

    // ----------------------- REFS -----------------------
	const successAlertRef = useRef<any>(null);
	const failAlertRef = useRef<any>(null);

    const sendPageForIndex = useMutation(
        sendPageForIndexingMutation.mutationFn as MutationFunction<AxiosResponse<any, any>, string[]>,
        {
        onSuccess: (res, variables) => {
            const url = variables[0];
            setProgressStatus(prev => ({ ...prev, [url]: false }));

            const success: string = res['data']['success'];
            const total_pages_sent: number = res['data']['total_pages'];

            if (success && total_pages_sent === 1) {
            successAlertRef.current?.show(`Page sent for indexing successfully!`);
            refetch();
            } else {
            const message: string = res['data']['message'] || "Oops! Something went wrong. Please try again after some time or contact us if the issue persists.";
            failAlertRef.current?.show(message);
            }
        },
        onError: (_error, variables) => {
            const url = variables[0];
            setProgressStatus(prev => ({ ...prev, [url]: false }));
            failAlertRef.current?.show(
            "Oops! Something went wrong. Please try again after some time or contact us if the issue persists."
            );
        },
        }
    );


    if (!pageData.indexation_done) {
        return (
            <BookLoader words={["posts", "articles", "blogs", "pages", "sitemap"]} />
        )
    } else if (isLoading){
        return (
			<p style={{ textAlign: "center", fontSize: "1.3rem" }}>
				Loading Data...<Icon iconName={"spinner"} marginClass={"ml-5"} />
			</p>
		)
    } else {
        // ================== Generate table data and render AbunTable component ==================
        const columnHelper = createColumnHelper<AllPages>();
    
        const columnDefs: ColumnDef<any, any>[] = [
            columnHelper.accessor((row: AllPages) => row.url, {
                id: 'checkbox',
                header: ({ table }) => (
                    <IndeterminateCheckbox
                        {...{
                            checked: table.getIsAllRowsSelected(),
                            indeterminate: table.getIsSomeRowsSelected(),
                            onChange: table.getToggleAllRowsSelectedHandler(),
                        }}
                    />
                ),
                cell: ({ row }) => (
                    <IndeterminateCheckbox
                        {...{
                            checked: row.getIsSelected(),
                            disabled: !row.getCanSelect(),
                            indeterminate: row.getIsSomeSelected(),
                            onChange: row.getToggleSelectedHandler(),
                        }}
                        name={"pagesSelection"}
                        value={row.original.url}
                    />
                ),
                enableGlobalFilter: true,
                enableSorting: false,
            }),
        
            columnHelper.accessor((row: AllPages) => row.url, {
                id: 'pageUrl',
                header: "URL",
                cell: info => info.getValue(),
                enableGlobalFilter: true,
                enableSorting: false,
            }),
    
            columnHelper.accessor((row: AllPages) => row.index, {
                id: 'index',
                header: "Status",
                cell: info => {
                    let displayText: string;
                    let btnColor: string;
                    if (info.row.original.in_progress){
                        displayText = 'In Progress';
					    btnColor = 'primary';                        
                    }else if (info.row.original.index){                        
                        displayText = 'Indexed';
					    btnColor = 'success';
                    } else if (info.row.original.status == "Not Indexed"){
                        displayText = 'Not Indexed';
					    btnColor = 'warning';
                    } else {
                        displayText = 'Rejected';
					    btnColor = 'danger';
                    }
                    return <button
                                className={`button is-small more-rounded-borders ${`is-${btnColor}`} custom-btn`}
                                disabled style={{cursor: 'pointer', opacity: "1.5", borderRadius: "4px"}}>{displayText}</button>

                },
                enableGlobalFilter: false,
                meta: {
                    align: 'center',
                }
            }),
            columnHelper.accessor((row) => row.last_crawled, {
                id: 'create_date',
                header: "Last Indexed Date",
                cell: (props) => {
                    const selectedDate = props.row.original.last_crawled
                    if (!selectedDate) return <span data-nonclickable="true" style={{ cursor: "default" }}>---</span>;
                    if (props.row.original.status == "Not Indexed") return <span data-nonclickable="true" style={{ cursor: "default" }}>---</span>;

                    const getRelativeTime = (dateString: string) => {
                    const createdDateObj = new Date(dateString);
                    const now = new Date();
                    const timeDiff = now.getTime() - createdDateObj.getTime();

                    const seconds = Math.floor(Math.abs(timeDiff) / 1000);
                    const minutes = Math.floor(seconds / 60);
                    const hours = Math.floor(minutes / 60);
                    const days = Math.floor(hours / 24);


                    // PAST TIME
                    if (seconds < 60) return "just now";
                    if (minutes < 60) return minutes === 1 ? "a minute ago" : `${minutes} minutes ago`;
                    if (hours < 24) return hours === 1 ? "an hour ago" : `${hours} hours ago`;
                    if (days > 30) {
                        const day = createdDateObj.getDate();
                        const month = createdDateObj.toLocaleString('default', { month: 'short' });
                        const year = createdDateObj.getFullYear().toString().slice(-2);
                        return `${day} ${month}, ${year}`;
                    }
                    return days === 1 ? "a day ago" : `${days} days ago`;
                    };

                    return <span data-nonclickable="true" style={{ cursor: "default", color: "#000" }}>{getRelativeTime(selectedDate)}</span>;
                },
                enableGlobalFilter: false,
                enableSorting: false,
                meta: {
                    align: 'center',
                }
                }),
            columnHelper.accessor((row: AllPages) => row.index, {
                id: 'index_page',
                header: () => "Index Page",
                cell: info => {
                    const url = info.row.original.url;
                    const isProcessing = progressStatus[url] || false;

                    const handleIndexClick = () => {
                    setProgressStatus(prev => ({ ...prev, [url]: true }));
                    sendPageForIndex.mutate([url]);
                    };

                    return (
                    <GenericButton
                        text={isProcessing ? "Is Processing..." : "Index"}
                        type="primary"
                        width="100px"
                        disable={isProcessing}
                        outlined={true}
                        additionalClassList={["is-small", "more-rounded-borders"]}
                        style={{ borderRadius: "5px" }}
                        clickHandler={handleIndexClick}
                    />
                    );
                },
                enableGlobalFilter: false,
                enableSorting: false,
                meta: { align: 'center' }
                }),
        ]

        function selectedRowsSetter(rowModel: RowModel<RowData>){
            setSelectedPages([]);
            rowModel.rows.forEach((row) => {
                const selectedPage = (row.original as AllPages);
                setSelectedPages((prevPage) => [...prevPage, selectedPage.url]);
            })
        }

        function safeDecode(value: string): string {
        try {
            return decodeURIComponent(value);
        } catch {
            return value;
        }
    }

        const defaultUrlFilter: FilterFn<any> = (row, columnId, filterValue) => {
                const rawValue = row.getValue(columnId);
        
                if (!rawValue || typeof filterValue !== 'string') return false;
        
                try {
                    const decoded = safeDecode(String(rawValue));
                    const target = decoded.toLowerCase();
                    const search = filterValue.toLowerCase();
        
                    return target.includes(search);
                } catch {
                    return false;
                }
            };

        return (
            <>
                {/* ------------------------------ ONGOING REQUEST MODAL ------------------------------ */}
				<AbunModal active={requestModalActive}
					headerText={""}
					closeable={false}
					hideModal={() => {
						setRequestModalActive(false)
					}}>
					<Player
						autoplay
						loop
						src="https://assets5.lottiefiles.com/private_files/lf30_tcux3hw6.json"
						style={{ height: '300px', width: '300px' }}
					>
					</Player>
					<p className={"is-size-4 has-text-centered mb-4"}>{modalText}</p>
				</AbunModal>
                {/* <div className="mt-4"> */}
                    <div className="mt-4 all-page">
                        <div className="content is-flex is-flex-direction-column">
                            <AbunTable
                                tableContentName={"ALL Pages"}
                                id={"indexation-all-pages"}
                                searchText={searchText}
                                tableData={tableData}
                                columnDefs={columnDefs}
                                pageSizes={pageSizes}
                                initialPageSize={pageSizes[1]}
                                enableSorting={true}
                                filterFn={defaultUrlFilter}
                                noDataText={"No Pages Found"}
                                searchboxPlaceholderText={"Search for Pages..."}
                                rowCheckbox={true}
                                selectedRowsSetter={selectedRowsSetter}
                                buttons={ !selectedPages.length ? [] : [
                                    {
                                        text: "Index Selected URLs",
                                        type: "success",
                                        clickHandler: () => {
                                            setRequestModalActive(true);
                                            successAlertRef.current?.show("");
                                            failAlertRef.current?.show("");

                                            sendPageForIndexing.mutate(selectedPages, {
                                                onSuccess: (res) => {
                                                    setRequestModalActive(false);
                                                    let total_pages: number = res['data']['total_pages'];
                                                    let success: string = res['data']['success'];

                                                    if (success){
                                                        if (total_pages !== 0){
                                                            successAlertRef.current?.show(`${total_pages} pages sent for indexing successfully!`);
                                                            refetch().then(() => {});
                                                        } else {
                                                            setRequestModalActive(false);
                                                            failAlertRef.current?.show(
                                                                "Oops! Something went wrong. Please try again after some time or contact us if the issue persists."
                                                            );
                                                        }
                                                    } else {
                                                        let message: string = res['data']['message'];
                                                        failAlertRef.current?.show(message); 
                                                    }
                                                },
                                                onError: () => {
                                                    setRequestModalActive(false);
                                                    failAlertRef.current?.show(
                                                        "Oops! Something went wrong. Please try again after some time or contact us if the issue persists."
                                                    );
                                                }
                                            })
                                        },
                                    }
                                ]}
                                
                                />
                        </div>
                        <div className="alerts">
                            <SuccessAlert ref={successAlertRef} />
                            <ErrorAlert ref={failAlertRef} />
                        </div>
                    </div>
                {/* </div> */}
            </>
        )
    }

}