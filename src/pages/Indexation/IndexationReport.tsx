import FormControlLabel from '@mui/material/FormControlLabel';
import FormGroup from '@mui/material/FormGroup';
import { styled } from '@mui/material/styles';
import Switch, { SwitchProps } from '@mui/material/Switch';
import { useQuery, useMutation } from "@tanstack/react-query";
import { useEffect, useState, useRef } from "react";
import { useLoaderData } from "react-router-dom";
import BookLoader from "../../components/BookLoader/BookLoader";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import Icon, { IconNames } from "../../components/Icon/Icon";
import { loadIndexationReportStatsQuery, saveSettingsMutation } from "../../utils/api";
import { Tooltip } from 'react-tooltip';
import './Indexation.min.css';

interface ServerData {
	user_verified: boolean
	has_active_website: boolean,
	integration_done: boolean,
	indexation_done: boolean,
	plan_name: string,
    current_active_website: string
	auto_indexing: boolean,
	indexed_allowed: boolean,
}

type Stats = {
    id: string
    icon_name: IconNames
    title: string
    count: number
}

interface CustomizedSwitchProps {    
    toggleCheck: boolean;
    handleToggle: () => void;
}

export default function IndexationReport({ errorRef, successRef }){
    // ----------------------------- LOADER -----------------------------
	const pageData: ServerData = useLoaderData() as ServerData;

    // ----------------------- STATES -----------------------
    const [indexationReportStats, setIndexationReportStats] = useState<Array<Stats>>([]);
    const [toggleSwitch, setToggleSwitch] = useState(pageData.auto_indexing)

    // ----------------------- EFFECTS -----------------------
    const { isLoading, error, data, refetch } = useQuery(loadIndexationReportStatsQuery());
    const saveSettings = useMutation(saveSettingsMutation);

    
    useEffect(() => {
		if (data) {
			setIndexationReportStats((data as any)['data']['index_stats_report']);
		}
	}, [data]);

            function handleToggleCheck() {            
                const nextValue = !toggleSwitch;
    
                setToggleSwitch(nextValue); //
                // Trigger save settings immediately
                saveSettings.mutate({
                    settingsToSave: [
                        { settingName: "auto_indexing", settingValue:  nextValue}
                    ]
                }, {
                    onSuccess: () => {                
                        successRef?.current?.show("Changes Saved!");
                        setTimeout(() => {
                            try {
                                if (successRef?.current) {
                                    successRef?.current?.close();
                                }
                            } catch (e) { }
                        }, 3000);
                    },
                    onError: () => {
                        errorRef?.current?.show("Oops! Something went wrong :( Please try again later or contact us for further support.");
                    }
                });
            }
    
            function CustomizedSwitch({ toggleCheck, handleToggle }: CustomizedSwitchProps) {
                        const IOSSwitch = styled((props: SwitchProps) => (
                            <Switch focusVisibleClassName=".Mui-focusVisible" disableRipple {...props} />
                        ))(({ theme }) => ({
                            width: 42,
                            height: 26,
                            padding: 0,
                            '& .MuiSwitch-switchBase': {
                                padding: 0,
                                margin: 2,
                                transitionDuration: '300ms',
                                '&.Mui-checked': {
                                    transform: 'translateX(16px)',
                                    color: '#fff',
                                    '& + .MuiSwitch-track': {
                                        backgroundColor: '#65C466',
                                        opacity: 1,
                                        border: 0,
                                        ...theme.applyStyles('dark', {
                                            backgroundColor: '#2ECA45',
                                        }),
                                    },
                                    '&.Mui-disabled + .MuiSwitch-track': {
                                        opacity: 0.5,
                                    },
                                },
                                '&.Mui-focusVisible .MuiSwitch-thumb': {
                                    color: '#33cf4d',
                                    border: '6px solid #fff',
                                },
                                '&.Mui-disabled .MuiSwitch-thumb': {
                                    color: theme.palette.grey[100],
                                    ...theme.applyStyles('dark', {
                                        color: theme.palette.grey[600],
                                    }),
                                },
                                '&.Mui-disabled + .MuiSwitch-track': {
                                    opacity: 0.7,
                                    ...theme.applyStyles('dark', {
                                        opacity: 0.3,
                                    }),
                                },
                            },
                            '& .MuiSwitch-thumb': {
                                boxSizing: 'border-box',
                                width: 22,
                                height: 22,
                            },
                            '& .MuiSwitch-track': {
                                borderRadius: 26 / 2,
                                backgroundColor: '#E9E9EA',
                                opacity: 1,
                                transition: theme.transitions.create(['background-color'], {
                                    duration: 500,
                                }),
                                ...theme.applyStyles('dark', {
                                    backgroundColor: '#39393D',
                                }),
                            },
                        }));
                
                        return (
                            <FormGroup sx={{ display: "flex", alignItems: "center", justifyContent: "center" }}>
                                <FormControlLabel
                                    control={<IOSSwitch sx={{ m: 1 }} defaultChecked={toggleCheck}
                                        onChange={handleToggle} />}
                                    label=""
                                />
                            </FormGroup>
                        );
                    }
    
    
    if (!pageData.indexation_done && isLoading) {
        return (
            <BookLoader words={["posts", "articles", "blogs", "pages", "sitemap"]} />
        )
    } else if (isLoading){
        return (
			<p style={{ textAlign: "center", fontSize: "1.3rem" }}>
				Loading Data...<Icon iconName={"spinner"} marginClass={"ml-5"} />
			</p>
		)
    } else {
        return (
            <>
                <div className={"indexation-report-container"}>
                    {indexationReportStats.map((stat) => (
                        <div className={"card mt-2"} key={stat.id}>
                            <div className={"card-content"}>
                                <div className={"content"}>
                                    <div className={"usage-stats-container"}>
                                        <p className={"usage-stats-item--title"}>
                                            <Icon iconName={stat.icon_name} />&nbsp;&nbsp;&nbsp;{stat.title}
                                        </p>
                                        <h4 style={{"fontSize": "2rem", color: "#000"}} className={"ml-4 mt-2 card-number"}>{stat.count}</h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                    {/* <div className='language-article-context'>
						<div className={"abun-table-button-container is-flex is-justify-content-end mr-4"}> */}
						<div className={"card mt-2"} >
							<div className={"card-content"} style={{paddingRight: "0px"}}>
								<div className={"content"} style={{ display: "flex", alignItems: "center", justifyContent: "space-between"}}>
									<div className={"usage-stats-container"}>
										<p className={"auto-indexing-content"} style={{fontSize: "15px", fontWeight: 600}}>
											Auto Indexing
										</p>
										<p className={"p-hour-text"} style={{fontWeight: 600}}>In 24 hrs.
											<span className="tooltip-question"
											data-tooltip-id="auto-index"
											data-tooltip-content="When enabled, Abun will automatically detect new pages every 24 hours and send them for indexing.">?
										</span>
										</p>										
									</div>
									<div>
									<CustomizedSwitch toggleCheck={toggleSwitch} handleToggle={() => handleToggleCheck()} />   
									</div>
								</div>
							</div>
						</div>					
						<Tooltip id="auto-index" place="bottom" />
					</div>
					{/* </div>
                </div> */}
            </>
        )
    }
}