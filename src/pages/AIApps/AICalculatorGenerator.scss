@import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";

@import "bulma/sass/utilities/all";
@import "bulma/sass/base/all";
@import "bulma/sass/helpers/typography";
@import "bulma/sass/grid/columns";
@import "bulma/sass/elements/container";
@import "bulma/sass/form/_all";
@import "bulma/sass/components/tabs";
@import "bulma/sass/helpers/all";
@import "bulma/sass/elements/all";

@import "../../assets/bulma-overrides/bulmaOverrides";

/* Main container for AI Calculator Generator */
.ai-calculator-generator-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: $primary-font !important;
  // max-width: 1500px;
  overflow: visible;
  width: 100%;
  padding: 0; // Remove any default padding
  margin: 0; // Remove any default margin

  /* Responsive adjustments */
  // @media (max-width: 480px) {
  //   height: auto;
  //   width: 100%;
  // }

  // @media (max-width: 769px) {
  //   height: auto;
  //   width: 100%;
  // }

  // @media (min-width: 1024px) {
  //   width: 100vw;
  //   max-width: 100%;
  // }

  /* Header section with back button */
  // .ai-calculator-generator-header {
  //   position: relative;
  //   width: 100%;
  //   height: 50px;

  //   .ai-calculator-back-button {
  //     background: none;
  //     border: none;
  //     cursor: pointer;
  //     display: flex;
  //     align-items: center;
  //     padding: 8px;
  //     border-radius: 4px;
  //     transition: background-color 0.2s;
  //     position: absolute;
  //     left: 0;

  //     &:hover {
  //       background-color: rgba(0, 0, 0, 0.05);
  //     }

  //     svg {
  //       width: 24px;
  //       height: 24px;
  //     }
  //   }
  // }

  /* Content section */
  .ai-calculator-generator-content {
    display: flex;
    flex-direction: column;
    // align-items: center;
    width: 100%;
    padding: 0; // Remove any default padding
    margin: 0; // Remove any default margin

    .ai-calculator-header {

      h2 {
        font-family: $primary-font !important;
        font-size: 2rem !important;
        font-weight: 600 !important;
        margin-bottom: 4px;
      }

      p {
        // color: rgba(0, 0, 0, .698);
        font-family: $secondary-font !important;
        font-size: 1.125rem !important;
      }
    }

    /* Input card for calculator type */
    .ai-calculator-form-container {
      border: 1px solid #e7e7e7;
      border-radius: 8px;
      margin-bottom: 1.5rem;
      width: fit-content;

      h3 {
        font-size: 1.4rem;
        font-weight: 600;
        padding: 1rem;
        align-self: start;
      }

      hr {
        background: #e7e7e7;
        height: 1px;
        margin: 0;
        width: 100%;
      }

      .ai-calculator-input-group {
        justify-items: center;
        display: flex;
        flex-direction: column;
        padding: 1rem;
        padding-left: 1.6rem;
        width: 100%;

        .textarea-box {
          height: 170px;
          min-height: 86px;
          margin-bottom: 16px;
          resize: vertical;
          width: 100%;
          max-width: 415px;
          border-radius: 10px;
          padding: 17px;
          font-family: $secondary-font;
        }

      }
    }

    /* Results section with chat and preview */
    .ai-calculator-result-section {
      gap: 0; // Remove gap to allow browser mockup to extend to edges
      width: 100%;
      display: flex;
      align-items: stretch; // Make both sides same height
      justify-content: space-between;
      height: calc(100vh - 200px); // Full viewport height minus header space
      min-height: 600px; // Minimum height for smaller screens

      @media (max-width: 1046px) {
        flex-direction: column;
        height: auto; // Let it be auto on mobile
      }

      .ai-horizontal-line {
        background-color: rgba(0, 0, 0, 0.1);
        width: 1.6px;
        height: 100%; // Match the full height of the result section
        margin: 0; // Remove all margins
        align-self: stretch; // Stretch to full height

        @media (max-width: 1046px) {
          height: 2px;
          width: 100%;
        }
      }

      /* Chat container */
      .ai-calculator-chat-container {
        margin-left: 5px;
        display: flex;

        .ai-content {
          display: flex;
          flex-direction: column;
          gap: 15px;
          flex-grow: 1;

          @media (max-width: 1046px) {
            width: 100%;
          }

          .title-text {
            font-size: 1.6rem;
            font-weight: 500;
            margin-left: 7px;
          }

          .custom-textarea {
            border-radius: 7px;
            height: 170px;
            min-height: 86px;
            resize: vertical;
          }

          .custom-button {
            width: fit-content;
          }

          input[type="checkbox"] {
            accent-color: #000000;
          }

        }
      }

      /* Embed container */
      .ai-embed-container {
        .ai-section {
          display: flex;
          flex-direction: column;
          gap: 12px;

          .textarea {
            border-radius: 10px;
          }

          .subtitle {
            font-size: 1.2rem;
            margin-left: 7px;
            font-weight: 600;
            margin-bottom: 0 !important;
          }


          .copy-btn {
            width: 19%;
            height: 36px;
            font-size: 18px;
            border-radius: 8px;
            margin-left: auto;
          }

        }
      }

      /* version container */
      .ai-version-container {
        margin-left: 12px;

        .custom-radio {
          margin-bottom: 0.8rem;
          appearance: none;
          width: 16px;
          height: 16px;
          border: 2px solid black;
          border-radius: 50%;
          position: relative;
          cursor: pointer;
          outline: none;
        }

        .custom-radio:checked::before {
          content: "";
          width: 8px;
          height: 8px;
          background-color: black;
          border-radius: 50%;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }

      /* Preview section */
      .ai-calculator-preview-section {
        display: flex;
        flex-direction: column;
        flex: 1; // Take up all remaining space after left section and line
        height: 100%; // Fill the full height of the result section
        margin: 0; // Remove all margins
        padding: 0; // Remove all padding
        box-sizing: border-box; // Ensure proper box model

        @media (max-width: 1046px) {
          width: 100%;
          margin-top: 0;
          flex: none;
        }
      }

      .ai-left-container {
        overflow-y: auto;
        margin-top: 0; // Remove margin to align with right section
        flex: 0 0 38%;
        height: 100%; // Fill the full height

        @media (max-width: 1046px) {
          width: 100%;
        }

        .tabs {
          scrollbar-width: none;

          ul {
            border-bottom: none !important;
            gap: 40px;
            justify-content: space-between;
            flex-wrap: wrap;

            @media (max-width: 768px) {
              gap: 15px;
              justify-content: center;
            }

            @media (max-width: 480px) {
              gap: 10px;
              flex-direction: column;
              align-items: center;
            }

            @media (min-width: 1570px) {
              justify-content: center;
            }

            li {
              @media (max-width: 480px) {
                width: 100%;
                text-align: center;
              }
            }

            li a {
              border-bottom: none !important;
              font-weight: 400;
              font-size: 1.2rem;
              padding: 0.3em 0em;
              white-space: nowrap;

              @media (max-width: 768px) {
                font-size: 1rem;
                padding: 0.4em 0.3em;
              }

              @media (max-width: 480px) {
                font-size: 0.95rem;
                padding: 0.5em 0.8em;
                width: 100%;
                text-align: center;
              }
            }

            li.is-active a {
              border-bottom: none !important;
              background: #D9EFFE;
              font-weight: 600;
              font-size: 1.2rem;
              border-radius: 8px;
              padding: 0.3em 0.6em;

              @media (max-width: 768px) {
                font-size: 1rem;
                padding: 0.4em 0.5em;
              }

              @media (max-width: 480px) {
                font-size: 0.95rem;
                padding: 0.5em 0.8em;
                width: 100%;
                text-align: center;
              }
            }
          }
        }
      }
    }

    .copy-button {
      position: absolute;
      right: 0px;
      top: -12px;
      background-color: #cfd6dd;
      color: #000;
      border: none;
      border-radius: 4px;
      padding: 5px 10px;
      cursor: pointer;
      font-size: 0.6rem;
      transition: background-color 0.3s;
      margin-left: auto; // Align to the right

      &:hover {
        background-color: darken(#cfd6dd, 5%);
      }
    }
  }

  .publish-calculator-modal {
    .modal-content {
      padding: 20px;
      border-radius: 8px;
      background-color: #fff;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    input[type="text"] {
      width: 100%;
      padding: 10px;
      margin-bottom: 20px;
      border: 1px solid #ccc;
      border-radius: 4px;
      box-sizing: border-box;

      &:focus {
        border-color: #0056b3;
        outline: none;
        box-shadow: 0 0 0 2px rgba(0, 86, 179, 0.25);
      }
    }

    .abun-button {
      display: block;
      width: 100%;
      padding: 10px;
      font-size: 16px;
      text-align: center;
      color: #fff;
      background-color: #007bff;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.3s;

      &:hover {
        background-color: #0056b3;
      }

      &:disabled {
        background-color: #ccc;
        cursor: not-allowed;
      }
    }

    .verification-results {
      margin-top: 20px;
      padding: 10px;
      border-radius: 4px;
      background-color: #f8f9fa;
      border-left: 5px solid;

      &.success {
        border-color: #28a745;
        color: #28a745;
      }

      &.error {
        border-color: #dc3545;
        color: #dc3545;
      }
    }

    code {
      display: block;
      padding: 10px;
      margin-top: 10px;
      background-color: #f4f4f4;
      border-radius: 4px;
      font-family: $secondary-font;
      white-space: pre-wrap;
    }
  }

  .horizontal-line {
    width: 100%;
    height: 1px;
    margin-bottom: 0;
    background-color: #dedbdb;
  }

  /* Browser Mockup Styles */
  .browser-mockup {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    background-color: #ffffff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    flex: 1; // Take up all available space in the preview section
    margin: 0; // Remove any default margins
    padding: 0; // Remove any default padding

    .browser-header {
      display: flex;
      align-items: center;
      background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
      padding: 12px 16px;
      border-bottom: 1px solid #d0d0d0;
      min-height: 44px;

      .browser-controls {
        display: flex;
        gap: 8px;
        margin-right: 16px;

        .browser-control {
          width: 12px;
          height: 12px;
          border-radius: 50%;

          &.close {
            background-color: #ff5f57;
            border: 1px solid #e0443e;
          }

          &.minimize {
            background-color: #ffbd2e;
            border: 1px solid #dea123;
          }

          &.maximize {
            background-color: #28ca42;
            border: 1px solid #1aab29;
          }
        }
      }

      .browser-address-bar {
        flex: 1;
        background-color: #ffffff;
        border: 1px solid #d0d0d0;
        border-radius: 6px;
        padding: 6px 12px;
        font-family: $secondary-font;

        .browser-url {
          color: #666666;
          font-size: 14px;
          font-weight: 400;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          display: block;
        }
      }
    }

    .browser-notification-bar {
      background: #E6EBEE;
      border-bottom: 1px solid #d0d0d0;
      padding: 8px 16px;
      color: #000;
      font-size: 13px;

      .notification-content {
        display: flex;
        align-items: center;
        justify-content: center;

        .notification-text {
          font-weight: 500;
          color: #000;
        }
      }
    }

    .browser-content {
      background-color: #ffffff;
      flex: 1;
      display: flex;
      flex-direction: column;
      position: relative;

      .ai-preview-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        border-bottom: 1px solid #eee;
        background-color: #fafafa;

        h3 {
          font-family: $primary-font;
          font-size: 1.25rem;
          margin: 0;
          color: #333;
        }

        .ai-preview-actions {
          display: flex;
          gap: 8px;

          .ai-action-button {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            border: none;
            background-color: #f0f0f0;
            color: $grey-darker;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.875rem;
            font-family: $secondary-font;
            transition: background-color 0.2s;

            &:hover {
              background-color: #e0e0e0;
            }

            svg {
              width: 16px;
              height: 16px;
            }
          }
        }
      }

      .ai-preview-container {
        padding: 0;
        flex: 1;
        overflow: hidden; // Remove scroll, let content fill the space
        border: none;
        display: flex;
        flex-direction: column;
        height: 100%;

        .ai-preview-content {
          width: 100%;
          height: 100%;
          flex: 1;
          overflow: hidden; // Prevent internal scrolling
        }
      }
    }

    /* Loading overlay for browser mockup */
    .browser-loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.95);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
      backdrop-filter: blur(2px);

      .browser-loading-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;

        .browser-loading-spinner {
          width: 40px;
          height: 40px;
          border: 3px solid #f3f3f3;
          border-top: 3px solid #2e64fe;
          border-radius: 50%;
          animation: browser-spin 1s linear infinite;
        }

        .browser-loading-text {
          color: #666;
          font-size: 16px;
          font-weight: 500;
          font-family: $primary-font;
          margin: 0;
        }
      }
    }

    @keyframes browser-spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    @media (max-width: 768px) {
      .browser-header {
        padding: 8px 12px;
        min-height: 36px;

        .browser-controls {
          gap: 6px;
          margin-right: 12px;

          .browser-control {
            width: 10px;
            height: 10px;
          }
        }

        .browser-address-bar {
          padding: 4px 8px;

          .browser-url {
            font-size: 12px;
          }
        }
      }
    }
  }
}