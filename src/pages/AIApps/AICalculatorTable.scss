@import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";

@import "bulma/sass/utilities/all";
@import "bulma/sass/base/all";
@import "bulma/sass/helpers/typography";
@import "bulma/sass/grid/columns";
@import "bulma/sass/elements/container";
@import "bulma/sass/form/_all";
@import "bulma/sass/components/tabs";
@import "bulma/sass/helpers/all";
@import "bulma/sass/elements/all";

@import "../../assets/bulma-overrides/bulmaOverrides";

.ai-calculator-table-tp-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  // padding: 20px 30px;
  font-family: $primary-font !important;
  overflow: visible;
  width: 100%;


    table {
      width: 100%;
      border-collapse: collapse;

      th {
        text-align: left;
        padding: 12px 15px;
        border-bottom: 1px solid #ddd;
        font-weight: 600;
        color: #000;
      }

      td {
        padding: 12px 15px;
        border-bottom: 1px solid #eee;
        max-width: 400px;
        font-size: 0.9em;
        color: #000;

        &:hover {
          cursor: pointer;
          text-decoration: none !important;
          color: $primary;
        }
      }

      .calculator-row {
        &:hover {
          background-color: #f9f9f9;
        }
      }
    }

    .pagination-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 20px;
      font-size: 14px;

      .pagination-controls {
        display: flex;
        align-items: center;
        gap: 8px;

        .select {
          margin: 0 8px;

          select {
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
          }
        }
      }

      .pagination-pages {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
}