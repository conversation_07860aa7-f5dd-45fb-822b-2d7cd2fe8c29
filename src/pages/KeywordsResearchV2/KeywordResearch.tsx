import { useMutation } from '@tanstack/react-query';
import { ColumnDef, createColumnHelper } from "@tanstack/react-table";
import { useContext, useEffect, useRef, useState } from 'react';
import { Helmet } from 'react-helmet';
import { NavLink, useLoaderData, useLocation, useNavigate, useRouteLoaderData, useSearchParams } from "react-router-dom";
import AbunButton from "../../components/AbunButton/AbunButton";
import AbunLoader from '../../components/AbunLoader/AbunLoader';
import AbunModal from "../../components/AbunModal/AbunModal";
import AbunTable from "../../components/AbunTable/AbunTable";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import GenericButton from "../../components/GenericButton/GenericButton";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import {
	aiKeywordsResearchMutation,
	getKeywordProject,
	getLongtailKeywordSuggestions,
	makeApiRequest,
	removeKeywordProjectMutation,
} from "../../utils/api";
import { hasDuplicates } from "../../utils/misc";
import '../CompetitorResearch/CompetitorResearch.min.css';
import './ResearchedKeywords.min.css';

import { pageURL } from "../routes";
import GSCKeywords2 from "./GSCKeywords2";

import Autocomplete from '@mui/material/Autocomplete';
import Box from '@mui/material/Box';
import TextField from '@mui/material/TextField';
import EarthFlag from '../../assets/images/earth-flag.webp';
import CustomContextMenu from '../../components/CustomContextMenu/CustomContextMenu';
import Survey from "../../components/Survey/Survey";
import { useUIState } from "../../hooks/UIStateContext";
import { BasePageData } from "../../pages/Base/Base";
import { SurveyContext } from "../../pages/Login/SurveyContext";
import countries from "../../utils/constants/CountriesforSerp";
import { ConnectWebsite } from "../ConnectWebsite/LazyConnectWebsiteModal";
import LazyCSVUpload from './CSVUpload/LazyCSVUpload';
import { LazyLoadGSCDomainsList, LazyLoadGSCIntegration } from "./GSC/LazyGSCComponents";
import { KeywordsProjectData, KeywordsProjectTableProps } from './LoadKeywordProject/KeywordProjectTypes';
import LazyLoadKeywordProject from "./LoadKeywordProject/LazyLoadKeywordProject";
import LazyTextAreaUpload from "./TextAreaUpload/LazyTextAreaUpload";
import IcpToKeyword from './IcpTokw';


// import "@fontsource/inter";

interface CountryType {
	location_code: number;
	location_name: string;
	country_iso_code: string;
	suggested?: boolean;
}

export interface PageData {
	has_active_website: boolean;
	has_gsc_integration: boolean;
	has_wp_integration: boolean;
	current_plan_name: string;
	articles_generated: number;
	country_code: string;
	selected_gsc_domain: string;
	current_active_website: string;
	regenerate_competitor: boolean;
}

interface EditableProject {
	name: string;
	isEditing: boolean;
}

export default function KeywordResearch() {
	const location = useLocation();
	const navigate = useNavigate();

	// ------------------ PAGE DATA ------------------
	const { pageData } = useLoaderData() as {
		pageData: PageData;
	};

	const basePageData: BasePageData = useRouteLoaderData("base") as BasePageData;
	// ------------------ NON STATE CONSTANTS ------------------
	const pageSizes = [5, 10, 15, 30, 50, 100, 500];

	// -------------------------- STATES --------------------------
	const [selectedLocation, setSelectedLocation] = useState<CountryType>({
		location_code: 1,
		location_name: 'Global',
		country_iso_code: 'ZZ',
	});
	const [inputKeywords, setInputKeywords] = useState(["", "", ""]);
	const tableRef = useRef<{ refetchData: () => Promise<void> }>(null);
	const [selectedTableRow, setSelectedTableRow] = useState<KeywordsProjectData | null>(null);
	const [showConnectWebsiteWarningModal, setShowConnectWebsiteWarningModal] = useState(false);
	const [showConnectWebsiteModal, setShowConnectWebsiteModal] = useState(false);
	const [deleteKwProjectId, setDeleteKwProjectId] = useState("")
	const [deleteKwProjectName, setDeleteKwProjectName] = useState("")
	const [showDeletePopUp, setShowDeletePopUp] = useState(false);
	const [selectedPage, setSelectedPage] = useState("");
	const [selectedDomain, setSelectedDomain] = useState(basePageData.active_website_domain || "");
	const [selectedKeywordRow, setSelectedKeywordRow] = useState<KeywordsProjectTableProps | null>(null);
	const [editableProjects, setEditableProjects] = useState<Record<string, EditableProject>>({});
    const [activeLongtailTab, setActiveLongtailTab] = useState("longtail-keywords");
    const [activeAITab, setActiveAITab] = useState("ai-keywords");
    const [activeManualTab, setActiveManualTab] = useState("manual-keywords");
	const [activeICPTab, setActiveICPTab] = useState("Icp-Keywords");
	// -------------------------- SURVEY CONTEXT --------------------------
	const context = useContext(SurveyContext);
	const { showSurvey } = context ? context : { showSurvey: false }

	// ----------------------- Mutations -----------------------
	const aiKeywordsResearchApi = useMutation(aiKeywordsResearchMutation);
	const removeKeywordProject = useMutation(removeKeywordProjectMutation);

	const { hamburgerActive } = useUIState();

	// ----------------------- EFFECTS -----------------------

	useEffect(() => {
		// Find the country that matches the `country_iso_code` with `pageData.country_code`
		if (pageData.country_code !== "ZZ") {
			const matchedCountry = countries.find(
				(country) => country.country_iso_code === pageData.country_code.toUpperCase()
			);
			// If a match is found, update the selected location
			if (matchedCountry) {
				setSelectedLocation(matchedCountry);
			}
		}
	}, [pageData.country_code]);


	useEffect(() => {
		// Parse the URL parameters (specifically the 'page' query parameter)
		const params = new URLSearchParams(location.search);
		const page = params.get('page'); // Get 'page' parameter from the URL

		// If the 'page' query param exists, update selectedPage accordingly
		if (page) {
			const pathMap: Record<string, string> = {
				"ai-keyword": "AI-keyword-research",
				"longtail": "longtail-keyword",
				"csv": "Import-Keywords-using-CSV",
				"gsc": "Import-GSC-Keywords",
				"manual": "Manual-Add-Keywords",
				"icp-keyword": "Icp-Keywords"
			};

			// Set the selected page based on the query parameter,
			setSelectedPage(pathMap[page] || "");
		} else {
			// If no query param, set the default page
			setSelectedPage("");
		}
	}, [location.search]);

	const handlePageChange = (page: string) => {
		navigate({
			pathname: '/keyword-research',
			search: `?page=${page}`,  // Add the page query parameter
		});
	};

	const [searchParams] = useSearchParams();
	const keywordProjectId = searchParams.get('keywordProjectId');

	if (keywordProjectId) {
		getKeywordProject(keywordProjectId).then((res) => {
			setSelectedTableRow(res.data.keyword_project);
			setSelectedPage("keyword-project-page");
			searchParams.delete('keywordProjectId');
		})
	}

	// ----------------------- REFS -----------------------
	const successAlertRef = useRef<any>(null);
	const failAlertRef = useRef<any>(null);

	// ------------------ TABLE COLUMN DEFS ------------------
	const columnHelper = createColumnHelper<KeywordsProjectData>();
	const columnDefs: ColumnDef<any, any>[] = [
		columnHelper.accessor((row: KeywordsProjectData) => row.projectName, {
			id: 'projectName',
			header: "Keyword Project",
			cell: props => {
				const projectId = props.row.original.projectId;
				const projectName = props.row.original.projectName;
				const project = editableProjects[projectId];

				if (project?.isEditing) {
					return (
						<input
							type="text"
							value={project.name}
							onChange={(e) => handleProjectNameChange(projectId, e.target.value)}
							onBlur={() => handleProjectSave(projectId, projectName)}
							onKeyDown={(e) => {
								if (e.key === "Enter") handleProjectSave(projectId, projectName);
							}}
							autoFocus
							style={{
								width: `${project.name.length}ch`,
								minWidth: "150px"
							}}
							onClick={(e) => e.stopPropagation()}
						/>
					);
				}

				return (
					<CustomContextMenu
						url={`keyword-research/?keywordProjectId=${props.row.original.projectId}`}
						CtrlOrMetaClick={() => {
							openUrlInNewTab(`/keyword-research/?keywordProjectId=${props.row.original.projectId}`);
						}}
						normalClick={() => {
							setSelectedTableRow(props.row.original);
							setSelectedPage("keyword-project-page");
						}}>
						<div className="keyword-project-name-container">
							<span>{props.row.original.projectName}</span>
							<button
								onClick={(e) => toggleProjectEdit(projectId, props.row.original.projectName, e)}
								className="project-edit-button"
							>
								✏️
							</button>
						</div>
					</CustomContextMenu>
				)
			},
		}),
		columnHelper.display({
			id: 'totalKeywords',
			header: "Total Keywords",
			cell: props => {
				if (props.row.original.totalKeywords > 0) {
					return (
						<span>{props.row.original.totalKeywords}</span>
					);
				}
			},
		}),
		columnHelper.display({
			id: 'totalTrafficVolume',
			header: "Total Traffic Volume",
			cell: props => {
				if (props.row.original.totalTrafficVolume > 0) {
					// convert to k, m, b
					let totalTrafficVolume = "" + props.row.original.totalTrafficVolume;
					if (props.row.original.totalTrafficVolume > 999 && props.row.original.totalTrafficVolume < 1000000) {
						totalTrafficVolume = (props.row.original.totalTrafficVolume / 1000).toFixed(2) + "K";
					} else if (props.row.original.totalTrafficVolume > 999999 && props.row.original.totalTrafficVolume < 1000000000) {
						totalTrafficVolume = (props.row.original.totalTrafficVolume / 1000000).toFixed(2) + "M";
					} else if (props.row.original.totalTrafficVolume > 999999999) {
						totalTrafficVolume = (props.row.original.totalTrafficVolume / 1000000000).toFixed(2) + "B";
					} else {
						totalTrafficVolume = "" + props.row.original.totalTrafficVolume;
					}
					return (
						<span>{totalTrafficVolume}</span>
					);
				}
			},
		}),
		columnHelper.display({
			id: 'delete',
			header: "Action",
			cell: (cellProps) => (
				<div style={{ textAlign: 'center' }}>
					<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"
						onClick={() => handleDelete(cellProps.row.original.projectId, cellProps.row.original.projectName)}>
						<g clip-path="url(#clip0_48_5565)">
							<g clip-path="url(#clip1_48_5565)">
								<path d="M3.15356 6.32313C3.44461 10.8562 3.72319 13.2144 3.88856 14.3369C3.97256 14.9046 4.34531 15.3672 4.90346 15.5011C5.66306 15.6839 6.9713 15.8906 9.00075 15.8906C11.0302 15.8906 12.3381 15.6839 13.098 15.5014C13.6559 15.3676 14.0286 14.9049 14.1126 14.3373C14.2783 13.2144 14.5566 10.8562 14.8476 6.32214" stroke="black" stroke-width="1.05" stroke-linecap="round" stroke-linejoin="round" />
								<path d="M11.3087 3.47962C12.4769 3.50128 13.3871 3.53672 14.0394 3.56986C14.8236 3.60923 15.552 4.02694 15.7712 4.78097C15.804 4.89417 15.8349 5.01394 15.8618 5.14092C15.9911 5.74467 15.5392 6.26344 14.924 6.31561C13.9331 6.39928 12.1195 6.49444 8.99249 6.49444C5.86579 6.49444 4.05191 6.39928 3.0613 6.31561C2.44574 6.26377 1.99129 5.74139 2.15043 5.14486C2.20785 4.92994 2.2784 4.73372 2.35255 4.55948C2.61932 3.93506 3.26146 3.61284 3.93937 3.57544C4.56543 3.54131 5.47663 3.50259 6.69135 3.47962C6.87108 3.07198 7.16548 2.7254 7.53869 2.48211C7.9119 2.23882 8.34781 2.10932 8.79332 2.10938H9.20741C9.65286 2.10938 10.0887 2.23891 10.4618 2.4822C10.835 2.72549 11.129 3.07203 11.3087 3.47962Z" stroke="black" stroke-width="1.05" stroke-linecap="round" stroke-linejoin="round" />
								<path d="M7.03125 9.32812L7.35937 12.6094" stroke="black" stroke-width="1.05" stroke-linecap="round" stroke-linejoin="round" />
								<path d="M10.9687 9.32812L10.6406 12.6094" stroke="black" stroke-width="1.05" stroke-linecap="round" stroke-linejoin="round" />
							</g>
						</g>
						<defs>
							<clipPath id="clip0_48_5565">
								<rect width="16.8" height="16.8" fill="white" transform="translate(0.600098 0.599976)" />
							</clipPath>
							<clipPath id="clip1_48_5565">
								<rect width="16.8" height="16.8" fill="white" transform="translate(0.600098 0.599976)" />
							</clipPath>
						</defs>
					</svg>
				</div>
			),
		}),
	];


	// -------------------------- FUNCTIONS --------------------------
	function handleDelete(projectId: string, projectName: string) {
		setDeleteKwProjectId(projectId)
		setDeleteKwProjectName(projectName)
		setShowDeletePopUp(true)
	}

	function handleBackBtnClick() {
		let icpSearch = ""
		if (selectedTableRow?.projectName?.startsWith('ICP-Keyword')){			
			icpSearch = "?page=icp-keyword"
			navigate(`/keyword-research${icpSearch}`);
			window.location.reload()
		}
		setSelectedTableRow(null);
		setSelectedPage("");
		setSelectedDomain("");

		if (!icpSearch){	
			navigate("/keyword-research", { replace: true });
		}
	}

	const addKeywordField = () => {
		setInputKeywords([...inputKeywords, ""]);
	};

	const removeKeywordField = (index: number) => {
		setInputKeywords(inputKeywords.filter((_, i) => i !== index));
	};

	const handleKeywordChange = (value: string, index: number) => {
		const newKeywords = [...inputKeywords];
		newKeywords[index] = value;
		setInputKeywords(newKeywords);
	};

	const openUrlInNewTab = (url: string) => {
		window.open(url, "_blank");
	}

	function aiKeywordsResearch() {
		// remove empty strings
		const filteredKeywords = inputKeywords.filter(keyword => keyword !== "");
		if (hasDuplicates(filteredKeywords)) {
			failAlertRef.current.show("Please enter unique keywords.");
			setTimeout(() => {
				failAlertRef.current?.close();
			}, 5000);
			return;
		}

		setActiveAITab("projects");
		setInputKeywords(["","",""]);
		
		aiKeywordsResearchApi.mutate(
			{
				keywords: filteredKeywords,
				selectedLocation: selectedLocation,
			},
			{
				onSuccess: (data) => {
					let responseData = (data as any)["data"];
					if (responseData["status"] === "rejected") {
						if (responseData["reason"] === "max_limit_reached") {
							failAlertRef.current?.show("Keywords generation request failed. " +
								"You have reached your max Keywords generation limit for the month.");
						} else if (responseData["reason"] === "blocked_keyword_used") {
							failAlertRef.current?.show(responseData["message"]);
						} else if (responseData["reason"] === "no_keywords_found") {
							failAlertRef.current?.show("No keywords found. Please try with different keywords.");
						} else {
							failAlertRef.current?.show(
								`Keywords generation request failed. Error ID: ${responseData["reason"]}`
							);
						}
						setTimeout(() => {
							failAlertRef.current?.close();
						}, 5000);
					} else {
						successAlertRef.current.show("Keywords Added Successfully!");
						setTimeout(() => {
							successAlertRef.current?.close();
						}, 5000);
						// handleBackBtnClick();
					}
				},
				onError: () => {
					failAlertRef.current.show("Error finding keywords. Please try again.");
					setTimeout(() => {
						failAlertRef.current?.close();
					}, 5000);
				}
			});
		return;
	}

	function addKeywordsDone() {
		successAlertRef.current?.show(
			`New Keywords were added successfully!`
		);
		setTimeout(() => {
			successAlertRef.current?.close();
		}, 5000);
		handleBackBtnClick();
	}

	function addKeywordsFailed(errorMessage: string) {
		failAlertRef.current?.show(errorMessage);
		setTimeout(() => {
			failAlertRef.current?.close();
		}, 5000);
	}

	const toggleProjectEdit = (projectId: string, projectName: string, e: React.MouseEvent) => {
		e.stopPropagation();
		setEditableProjects(prev => ({
			...prev,
			[projectId]: {
				name: projectName,
				isEditing: !prev[projectId]?.isEditing
			}
		}));
	};

	const handleProjectNameChange = (projectId: string, value: string) => {
		setEditableProjects(prev => ({
			...prev,
			[projectId]: {
				...prev[projectId],
				name: value
			}
		}));
	};

	const handleProjectSave = async (projectId: string, projectName: string) => {
		// Close alert refs
		failAlertRef.current?.close();
		successAlertRef.current?.close();

		const project = editableProjects[projectId];

		if (!project || project.name === projectName) {
			// Exit edit mode
			setEditableProjects(prev => ({
				...prev,
				[projectId]: {
					...prev[projectId],
					isEditing: false
				}
			}));
			return;
		} else if (project.name.trim().length < 5 || project.name.trim().length > 60) {
			failAlertRef.current?.show("Oops! Project name must be 5-60 characters long.");
			return;
		}

		try {
			// Save the project name
			makeApiRequest(`/api/frontend/rename-keyword-project/`, 'post', {
				project_id: projectId,
				project_name: project.name,
			}).then((response: any) => {
				if (response.status === 200) {
					// Exit edit mode
					setEditableProjects(prev => ({
						...prev,
						[projectId]: {
							...prev[projectId],
							isEditing: false
						}
					}));

					if (tableRef.current) {
						// Refresh the table data
						tableRef.current.refetchData();
					} else {
						// Refresh the current route
						navigate(0);
					}
				} else {
					failAlertRef.current?.show("Oops! Something went wrong. Please try again in some time.");
				}
			}).catch((error) => {
				console.error('Error saving project name:', error);
				failAlertRef.current?.show("Server Error. Please try again in some time.");
			});
		} catch (error) {
			console.error('Error saving project name:', error);
			failAlertRef.current?.show("Server Error. Please try again in some time.");
		}
	};

	return (
		<>
			{/* {
				selectedPage === "" && closablePopup &&
				<div className={"tipcard card is-flex w-100 is-align-items-center is-justify-content-space-between is-flex-direction-rows mb-4"}>
					<img src="https://res.cloudinary.com/diaiivikl/image/upload/v1722685153/handy-ezgif.com-gif-to-webp-converter_fq22f0.webp" alt="printer" />
					<div className={"tipcard-content is-flex is-justify-content-center is-align-items-center is-flex-direction-column"}>
						<h2 className={"is-size-4"}>Creating Articles is Easy Peasy!</h2>
						<h6>
							Find Keywords &gt; Choose Keywords to Write Articles for &gt; Choose Relevant Titles &gt; Generate Articles 🎉
						</h6>
					</div>
					<div style={{ marginRight: "3%" }} className={"btn-container"}>
						<Link className={"button is-primary mt-2"} type="link" to={pageURL['tutorials']}>Watch Video Tutorial</Link>
					</div>
					<div className={"close-popup"} onClick={() => setClosablePopup(false)}>
						x
					</div>
				</div>
			} */}

			{
				selectedPage === "" &&
				<div className={"keyword-research-container is-flex w-100 is-align-items-center is-justify-content-flex-start is-flex-direction-column"}>
					<Helmet>
						<title>Keyword Research Projects | Abun.com</title>
						<meta
						  name="description"
						  content="Save and manage multiple keyword research campaigns in one place."
						/>
					</Helmet>
					<div className={"is-flex is-justify-content-center is-align-items-center is-flex-direction-column"}>
						<h2>Keyword Research</h2>
						<p>Find the right keywords to rank for & write articles for it. </p>
					</div>

					<div className={"menu-btns-container"}>
						<div className={"menu-btns AI-keyword-research-btn"} onClick={() => handlePageChange("ai-keyword")}>
							<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="-0.5 -0.5 24 24" height="24" width="24">
								<g>
									<path id="Subtract" fill="#ffffff" d="M8.775266666666667 6.45940625C6.912027083333334 7.2288041666666665 4.91625 8.039314583333333 3.5038104166666666 8.576460416666668L4.593962500000001 12.644824999999999C6.08565625 12.40375625 8.219385416666668 12.107775 10.217654166666666 11.842508333333335C10.016020833333332 11.258068750000001 9.754012500000002 10.407308333333333 9.422716666666666 9.170722916666668C9.091325 7.934089583333334 8.892854166666668 7.066366666666666 8.775266666666667 6.45940625Z" strokeWidth="1"></path>
									<path id="hover" fill="#ffffff" d="M8.710387500000001 6.10549375C8.597879166666667 5.446160416666666 8.939045833333333 4.901827083333334 9.53191875 4.5922375C11.64145 3.4905375000000003 14.51831875 2.2307604166666666 17.743972916666667 1.2374479166666668C18.68706875 0.947025 19.67223541666667 1.4303604166666668 19.99275 2.3636333333333335C20.286527083333333 3.219329166666667 20.66938125 4.433345833333334 21.100295833333334 6.04166875C21.531258333333337 7.64994375 21.806731250000002 8.892758333333335 21.980093749999998 9.780702083333333C22.16926875 10.74919375 21.557708333333334 11.660377083333335 20.59578125 11.880410416666667C17.305583333333335 12.632989583333334 14.184195833333334 12.980433333333334 11.806522916666667 13.081106250000001C11.138277083333334 13.109425 10.57065625 12.808604166666669 10.338404166666667 12.181375000000001C10.124456250000001 11.603547916666667 9.824545833333334 10.671185416666667 9.422525 9.170722916666668S8.814031250000001 6.7128854166666665 8.710387500000001 6.10549375Z" strokeWidth="1"></path>
									<path id="hover" fill="#ffffff" d="M3.3843541666666668 8.177793750000001C3.230829166666667 7.67495625 2.7970875 7.338533333333334 2.2819833333333337 7.443902083333334C2.1785312500000003 7.465129166666666 2.0650645833333336 7.491675000000001 1.94134375 7.5248333333333335C1.8175750000000002 7.55794375 1.7060250000000001 7.591725 1.6058791666666667 7.625122916666666C1.1070666666666666 7.791393750000001 0.8996354166666667 8.299597916666668 1.0181333333333333 8.811875C1.1355770833333334 9.319839583333334 1.3277229166666669 10.100689583333333 1.6375041666666668 11.256775000000001C1.9472854166666669 12.412908333333334 2.171295833333333 13.185181250000001 2.323575 13.683850000000001C2.4771 14.186735416666666 2.9108416666666668 14.523158333333335 3.4259458333333335 14.417741666666668C3.529397916666667 14.396610416666668 3.6428645833333335 14.370016666666666 3.7665854166666666 14.336858333333334C3.8903541666666666 14.3037 4.001904166666667 14.269966666666667 4.10205 14.23656875C4.600910416666667 14.070297916666668 4.808293750000001 13.56209375 4.689795833333333 13.04976875C4.572352083333333 12.541804166666667 4.380158333333333 11.761002083333333 4.070425 10.604868750000001C3.7606437500000003 9.448783333333335 3.5366333333333335 8.676510416666668 3.3843541666666668 8.177793750000001Z" strokeWidth="1"></path>
									<path id="Rectangle 1097" stroke="#2859c5" strokeLinejoin="round" d="M8.710387500000001 6.10549375C8.597879166666667 5.446160416666666 8.939045833333333 4.901827083333334 9.53191875 4.5922375C11.64145 3.4905375000000003 14.51831875 2.2307604166666666 17.743972916666667 1.2374479166666668C18.68706875 0.947025 19.67223541666667 1.4303604166666668 19.99275 2.3636333333333335C20.286527083333333 3.219329166666667 20.66938125 4.433345833333334 21.100295833333334 6.04166875C21.531258333333337 7.64994375 21.806731250000002 8.892758333333335 21.980093749999998 9.780702083333333C22.16926875 10.74919375 21.557708333333334 11.660377083333335 20.59578125 11.880410416666667C17.305583333333335 12.632989583333334 14.184195833333334 12.980433333333334 11.806522916666667 13.081106250000001C11.138277083333334 13.109425 10.57065625 12.808604166666669 10.338404166666667 12.181375000000001C10.124456250000001 11.603547916666667 9.824545833333334 10.671185416666667 9.422525 9.170722916666668S8.814031250000001 6.7128854166666665 8.710387500000001 6.10549375Z" strokeWidth="1"></path>
									<path id="Subtract_2" stroke="#2859c5" strokeLinejoin="round" d="M8.775266666666667 6.45940625C6.912027083333334 7.2288041666666665 4.91625 8.039314583333333 3.5038104166666666 8.576460416666668" strokeWidth="1"></path>
									<path id="Subtract_3" stroke="#2859c5" strokeLinejoin="round" d="M4.593962500000001 12.644920833333334C6.085704166666667 12.4039 8.219385416666668 12.10791875 10.217654166666666 11.842604166666668" strokeWidth="1"></path>
									<path id="Rectangle 135" stroke="#2859c5" strokeLinejoin="round" d="M3.3843541666666668 8.177793750000001C3.230829166666667 7.67495625 2.7970875 7.338533333333334 2.2819833333333337 7.443902083333334C2.1785312500000003 7.465129166666666 2.0650645833333336 7.491675000000001 1.94134375 7.5248333333333335C1.8175750000000002 7.55794375 1.7060250000000001 7.591725 1.6058791666666667 7.625122916666666C1.1070666666666666 7.791393750000001 0.8996354166666667 8.299597916666668 1.0181333333333333 8.811875C1.1355770833333334 9.319839583333334 1.3277229166666669 10.100689583333333 1.6375041666666668 11.256775000000001C1.9472854166666669 12.412908333333334 2.171295833333333 13.185181250000001 2.323575 13.683850000000001C2.4771 14.186735416666666 2.9108416666666668 14.523158333333335 3.4259458333333335 14.417741666666668C3.529397916666667 14.396610416666668 3.6428645833333335 14.370016666666666 3.7665854166666666 14.336858333333334C3.8903541666666666 14.3037 4.001904166666667 14.269966666666667 4.10205 14.23656875C4.600910416666667 14.070297916666668 4.808293750000001 13.56209375 4.689795833333333 13.04976875C4.572352083333333 12.541804166666667 4.380158333333333 11.761002083333333 4.070425 10.604868750000001C3.7606437500000003 9.448783333333335 3.5366333333333335 8.676510416666668 3.3843541666666668 8.177793750000001Z" strokeWidth="1"></path>
									<path id="Vector 1661" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="M9.857656250000002 21.846597916666667L13.88754375 13.283027083333334L17.917479166666666 21.846597916666667" strokeWidth="1"></path>
								</g>
							</svg>
							<span className={"menu-btn-text"}>AI Keyword Research</span>
						</div>

						{/* {pageData.current_plan_name === "Trial" ?
							<NavLink to={pageURL['manageSubscription']} className={"menu-btns Steal-Competitor-Keywords-btn"} >
								<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="-0.5 -0.5 24 24" id="Magnet--Streamline-Plump" height="24" width="24">
									<g>
										<path id="hover" fill="#ffffff" fillRule="evenodd" d="M12.4967625 1.9889920833333337c-0.3524270833333334 -0.36072145833333336 -0.8847812500000001 -0.44720625 -1.3501479166666666 -0.25291854166666666 -1.499025 0.6257916666666667 -2.9284270833333337 1.4213904166666667 -4.269758333333334 2.3313614583333333C1.456719375 7.744531250000001 -0.798516875 13.722758333333333 4.281196041666667 18.849122916666666 9.196070833333334 23.80916875 15.464672916666666 21.388370833333333 18.934079166666667 16.2538125c0.9135312500000001 -1.3520166666666669 1.7240895833333334 -2.8197520833333334 2.3476291666666667 -4.346952083333333 0.18376041666666668 -0.45003333333333334 0.10431458333333334 -0.9629333333333333 -0.24375208333333337 -1.3023270833333336 -0.65075625 -0.6345125 -1.8894020833333334 -1.6141208333333332 -3.6165583333333333 -1.9810187499999998 -0.5312041666666667 -0.11284374999999999 -1.0528729166666666 0.16722916666666665 -1.3360604166666668 0.6305833333333334 -0.5349895833333334 0.8754375 -1.418525 2.305845833333333 -1.9957291666666668 3.1601041666666667 -1.6949083333333335 2.508341666666667 -4.031229166666667 3.5762125 -5.503468750000001 2.0904604166666667 -1.4722395833333333 -1.4858 -0.5372895833333333 -3.84804375 2.0336312500000004 -5.592162500000001 0.8812833333333333 -0.5979041666666667 2.3899395833333337 -1.5006541666666668 3.2603458333333335 -2.0144166666666665 0.41936666666666667 -0.24748958333333332 0.7040875000000001 -0.6909583333333333 0.6407416666666667 -1.1737666666666668 -0.21658333333333335 -1.6501445833333332 -1.3205354166666667 -3.015228125 -2.0240958333333334 -3.7353245833333335Z" clipRule="evenodd" strokeWidth="1"></path>
										<path id="Intersect" fill="#ffffff" d="M19.8481375 14.800883333333333c0.5417937500000001 -0.9289604166666667 1.0274770833333333 -1.8987458333333334 1.4337145833333336 -2.893735416666667 0.18376041666666668 -0.45008125000000004 0.10431458333333334 -0.96298125 -0.24375208333333337 -1.3023270833333336 -0.65075625 -0.6345125 -1.8894020833333334 -1.6141687500000002 -3.6165583333333333 -1.981066666666667 -0.5312041666666667 -0.11284374999999999 -1.0528729166666666 0.16722916666666665 -1.3360604166666668 0.6305833333333334 -0.37983541666666665 0.6215270833333333 -0.9353333333333333 1.5228395833333335 -1.4321812500000002 2.2990416666666667 1.7753125 1.57564375 3.9007041666666664 2.6676166666666665 5.1948375 3.247504166666667Z" strokeWidth="1"></path>
										<path id="Intersect_2" fill="#ffffff" d="M11.52975625 8.322454166666667c0.8049520833333333 -0.5059520833333334 1.7365479166666666 -1.0619770833333333 2.3505520833333335 -1.4243708333333334 0.41936666666666667 -0.24748958333333332 0.7040875000000001 -0.6909583333333333 0.6407416666666667 -1.1737666666666668 -0.21658333333333335 -1.6501445833333332 -1.3205354166666667 -3.015228125 -2.0240958333333334 -3.7353245833333335 -0.3524270833333334 -0.36072145833333336 -0.8847812500000001 -0.44720625 -1.3501479166666666 -0.25291854166666666 -0.9193770833333335 0.38380770833333333 -1.8125437500000001 0.8314883333333334 -2.6752833333333332 1.3302529166666668 0.5289041666666666 1.2604047916666667 1.55753125 3.4144027083333333 3.0582333333333334 5.2561277083333335Z" strokeWidth="1"></path>
										<path id="Rectangle 650 (Stroke)_2" fillRule="evenodd" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="M12.4967625 1.9889920833333337c-0.3524270833333334 -0.36072145833333336 -0.8847812500000001 -0.44720625 -1.3501479166666666 -0.25291854166666666 -1.499025 0.6257916666666667 -2.9284270833333337 1.4213904166666667 -4.269758333333334 2.3313614583333333C1.456719375 7.744531250000001 -0.798516875 13.722758333333333 4.281196041666667 18.849122916666666 9.196070833333334 23.80916875 15.464672916666666 21.388370833333333 18.934079166666667 16.2538125c0.9135312500000001 -1.3520166666666669 1.7240895833333334 -2.8197520833333334 2.3476291666666667 -4.346952083333333 0.18376041666666668 -0.45003333333333334 0.10431458333333334 -0.9629333333333333 -0.24375208333333337 -1.3023270833333336 -0.65075625 -0.6345125 -1.8894020833333334 -1.6141208333333332 -3.6165583333333333 -1.9810187499999998 -0.5312041666666667 -0.11284374999999999 -1.0528729166666666 0.16722916666666665 -1.3360604166666668 0.6305833333333334 -0.5349895833333334 0.8754375 -1.418525 2.305845833333333 -1.9957291666666668 3.1601041666666667 -1.6949083333333335 2.508341666666667 -4.031229166666667 3.5762125 -5.503468750000001 2.0904604166666667 -1.4722395833333333 -1.4858 -0.5372895833333333 -3.84804375 2.0336312500000004 -5.592162500000001 0.8812833333333333 -0.5979041666666667 2.3899395833333337 -1.5006541666666668 3.2603458333333335 -2.0144166666666665 0.41936666666666667 -0.24748958333333332 0.7040875000000001 -0.6909583333333333 0.6407416666666667 -1.1737666666666668 -0.21658333333333335 -1.6501445833333332 -1.3205354166666667 -3.015228125 -2.0240958333333334 -3.7353245833333335Z" clipRule="evenodd" strokeWidth="1"></path>
										<path id="Intersect_3" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="M19.84799375 14.800691666666667c-1.2941333333333334 -0.5798875 -3.4195729166666666 -1.6718604166666668 -5.194885416666667 -3.247504166666667m-3.1235916666666665 -3.2306375000000003c-1.5007020833333333 -1.8417729166666668 -2.529329166666667 -3.9957612499999997 -3.0582333333333334 -5.256166041666667" strokeWidth="1"></path>
									</g>
								</svg>
								<span className={"menu-btn-text"}>
									Steal Competitor Keywords
									<svg xmlns="http://www.w3.org/2000/svg" viewBox="-0.5 -0.5 24 24" height="24" width="24">
										<g>
											<path d="M5.03125 9.34375h12.9375s1.4375 0 1.4375 1.4375v10.0625s0 1.4375 -1.4375 1.4375H5.03125s-1.4375 0 -1.4375 -1.4375v-10.0625s0 -1.4375 1.4375 -1.4375" fill="none" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1"></path>
											<path d="M6.46875 9.34375V5.75a5.03125 5.03125 0 0 1 10.0625 0v3.59375" fill="none" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1"></path>
											<path d="M9.34375 15.8125a2.15625 2.15625 0 1 0 4.3125 0 2.15625 2.15625 0 1 0 -4.3125 0" fill="none" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1"></path>
										</g>
									</svg>
								</span>
							</NavLink>
							:							 */}
						{
							pageData.has_active_website ?
								<NavLink to={pageURL['competitorResearch']} id="competitorResearch" className={"menu-btns Steal-Competitor-Keywords-btn"}>
									<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="-0.5 -0.5 24 24" id="Magnet--Streamline-Plump" height="24" width="24">
										<g>
											<path id="hover" fill="#ffffff" fillRule="evenodd" d="M12.4967625 1.9889920833333337c-0.3524270833333334 -0.36072145833333336 -0.8847812500000001 -0.44720625 -1.3501479166666666 -0.25291854166666666 -1.499025 0.6257916666666667 -2.9284270833333337 1.4213904166666667 -4.269758333333334 2.3313614583333333C1.456719375 7.744531250000001 -0.798516875 13.722758333333333 4.281196041666667 18.849122916666666 9.196070833333334 23.80916875 15.464672916666666 21.388370833333333 18.934079166666667 16.2538125c0.9135312500000001 -1.3520166666666669 1.7240895833333334 -2.8197520833333334 2.3476291666666667 -4.346952083333333 0.18376041666666668 -0.45003333333333334 0.10431458333333334 -0.9629333333333333 -0.24375208333333337 -1.3023270833333336 -0.65075625 -0.6345125 -1.8894020833333334 -1.6141208333333332 -3.6165583333333333 -1.9810187499999998 -0.5312041666666667 -0.11284374999999999 -1.0528729166666666 0.16722916666666665 -1.3360604166666668 0.6305833333333334 -0.5349895833333334 0.8754375 -1.418525 2.305845833333333 -1.9957291666666668 3.1601041666666667 -1.6949083333333335 2.508341666666667 -4.031229166666667 3.5762125 -5.503468750000001 2.0904604166666667 -1.4722395833333333 -1.4858 -0.5372895833333333 -3.84804375 2.0336312500000004 -5.592162500000001 0.8812833333333333 -0.5979041666666667 2.3899395833333337 -1.5006541666666668 3.2603458333333335 -2.0144166666666665 0.41936666666666667 -0.24748958333333332 0.7040875000000001 -0.6909583333333333 0.6407416666666667 -1.1737666666666668 -0.21658333333333335 -1.6501445833333332 -1.3205354166666667 -3.015228125 -2.0240958333333334 -3.7353245833333335Z" clipRule="evenodd" strokeWidth="1"></path>
											<path id="Intersect" fill="#ffffff" d="M19.8481375 14.800883333333333c0.5417937500000001 -0.9289604166666667 1.0274770833333333 -1.8987458333333334 1.4337145833333336 -2.893735416666667 0.18376041666666668 -0.45008125000000004 0.10431458333333334 -0.96298125 -0.24375208333333337 -1.3023270833333336 -0.65075625 -0.6345125 -1.8894020833333334 -1.6141687500000002 -3.6165583333333333 -1.981066666666667 -0.5312041666666667 -0.11284374999999999 -1.0528729166666666 0.16722916666666665 -1.3360604166666668 0.6305833333333334 -0.37983541666666665 0.6215270833333333 -0.9353333333333333 1.5228395833333335 -1.4321812500000002 2.2990416666666667 1.7753125 1.57564375 3.9007041666666664 2.6676166666666665 5.1948375 3.247504166666667Z" strokeWidth="1"></path>
											<path id="Intersect_2" fill="#ffffff" d="M11.52975625 8.322454166666667c0.8049520833333333 -0.5059520833333334 1.7365479166666666 -1.0619770833333333 2.3505520833333335 -1.4243708333333334 0.41936666666666667 -0.24748958333333332 0.7040875000000001 -0.6909583333333333 0.6407416666666667 -1.1737666666666668 -0.21658333333333335 -1.6501445833333332 -1.3205354166666667 -3.015228125 -2.0240958333333334 -3.7353245833333335 -0.3524270833333334 -0.36072145833333336 -0.8847812500000001 -0.44720625 -1.3501479166666666 -0.25291854166666666 -0.9193770833333335 0.38380770833333333 -1.8125437500000001 0.8314883333333334 -2.6752833333333332 1.3302529166666668 0.5289041666666666 1.2604047916666667 1.55753125 3.4144027083333333 3.0582333333333334 5.2561277083333335Z" strokeWidth="1"></path>
											<path id="Rectangle 650 (Stroke)_2" fillRule="evenodd" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="M12.4967625 1.9889920833333337c-0.3524270833333334 -0.36072145833333336 -0.8847812500000001 -0.44720625 -1.3501479166666666 -0.25291854166666666 -1.499025 0.6257916666666667 -2.9284270833333337 1.4213904166666667 -4.269758333333334 2.3313614583333333C1.456719375 7.744531250000001 -0.798516875 13.722758333333333 4.281196041666667 18.849122916666666 9.196070833333334 23.80916875 15.464672916666666 21.388370833333333 18.934079166666667 16.2538125c0.9135312500000001 -1.3520166666666669 1.7240895833333334 -2.8197520833333334 2.3476291666666667 -4.346952083333333 0.18376041666666668 -0.45003333333333334 0.10431458333333334 -0.9629333333333333 -0.24375208333333337 -1.3023270833333336 -0.65075625 -0.6345125 -1.8894020833333334 -1.6141208333333332 -3.6165583333333333 -1.9810187499999998 -0.5312041666666667 -0.11284374999999999 -1.0528729166666666 0.16722916666666665 -1.3360604166666668 0.6305833333333334 -0.5349895833333334 0.8754375 -1.418525 2.305845833333333 -1.9957291666666668 3.1601041666666667 -1.6949083333333335 2.508341666666667 -4.031229166666667 3.5762125 -5.503468750000001 2.0904604166666667 -1.4722395833333333 -1.4858 -0.5372895833333333 -3.84804375 2.0336312500000004 -5.592162500000001 0.8812833333333333 -0.5979041666666667 2.3899395833333337 -1.5006541666666668 3.2603458333333335 -2.0144166666666665 0.41936666666666667 -0.24748958333333332 0.7040875000000001 -0.6909583333333333 0.6407416666666667 -1.1737666666666668 -0.21658333333333335 -1.6501445833333332 -1.3205354166666667 -3.015228125 -2.0240958333333334 -3.7353245833333335Z" clipRule="evenodd" strokeWidth="1"></path>
											<path id="Intersect_3" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="M19.84799375 14.800691666666667c-1.2941333333333334 -0.5798875 -3.4195729166666666 -1.6718604166666668 -5.194885416666667 -3.247504166666667m-3.1235916666666665 -3.2306375000000003c-1.5007020833333333 -1.8417729166666668 -2.529329166666667 -3.9957612499999997 -3.0582333333333334 -5.256166041666667" strokeWidth="1"></path>
										</g>
									</svg>
									<span className={"menu-btn-text"}>Steal Competitor Keywords</span>
								</NavLink>
								:
								<div className={"menu-btns Steal-Competitor-Keywords-btn"} onClick={() => { setShowConnectWebsiteWarningModal(true) }}>
									<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="-0.5 -0.5 24 24" id="Magnet--Streamline-Plump" height="24" width="24">
										<g>
											<path id="hover" fill="#ffffff" fillRule="evenodd" d="M12.4967625 1.9889920833333337c-0.3524270833333334 -0.36072145833333336 -0.8847812500000001 -0.44720625 -1.3501479166666666 -0.25291854166666666 -1.499025 0.6257916666666667 -2.9284270833333337 1.4213904166666667 -4.269758333333334 2.3313614583333333C1.456719375 7.744531250000001 -0.798516875 13.722758333333333 4.281196041666667 18.849122916666666 9.196070833333334 23.80916875 15.464672916666666 21.388370833333333 18.934079166666667 16.2538125c0.9135312500000001 -1.3520166666666669 1.7240895833333334 -2.8197520833333334 2.3476291666666667 -4.346952083333333 0.18376041666666668 -0.45003333333333334 0.10431458333333334 -0.9629333333333333 -0.24375208333333337 -1.3023270833333336 -0.65075625 -0.6345125 -1.8894020833333334 -1.6141208333333332 -3.6165583333333333 -1.9810187499999998 -0.5312041666666667 -0.11284374999999999 -1.0528729166666666 0.16722916666666665 -1.3360604166666668 0.6305833333333334 -0.5349895833333334 0.8754375 -1.418525 2.305845833333333 -1.9957291666666668 3.1601041666666667 -1.6949083333333335 2.508341666666667 -4.031229166666667 3.5762125 -5.503468750000001 2.0904604166666667 -1.4722395833333333 -1.4858 -0.5372895833333333 -3.84804375 2.0336312500000004 -5.592162500000001 0.8812833333333333 -0.5979041666666667 2.3899395833333337 -1.5006541666666668 3.2603458333333335 -2.0144166666666665 0.41936666666666667 -0.24748958333333332 0.7040875000000001 -0.6909583333333333 0.6407416666666667 -1.1737666666666668 -0.21658333333333335 -1.6501445833333332 -1.3205354166666667 -3.015228125 -2.0240958333333334 -3.7353245833333335Z" clipRule="evenodd" strokeWidth="1"></path>
											<path id="Intersect" fill="#ffffff" d="M19.8481375 14.800883333333333c0.5417937500000001 -0.9289604166666667 1.0274770833333333 -1.8987458333333334 1.4337145833333336 -2.893735416666667 0.18376041666666668 -0.45008125000000004 0.10431458333333334 -0.96298125 -0.24375208333333337 -1.3023270833333336 -0.65075625 -0.6345125 -1.8894020833333334 -1.6141687500000002 -3.6165583333333333 -1.981066666666667 -0.5312041666666667 -0.11284374999999999 -1.0528729166666666 0.16722916666666665 -1.3360604166666668 0.6305833333333334 -0.37983541666666665 0.6215270833333333 -0.9353333333333333 1.5228395833333335 -1.4321812500000002 2.2990416666666667 1.7753125 1.57564375 3.9007041666666664 2.6676166666666665 5.1948375 3.247504166666667Z" strokeWidth="1"></path>
											<path id="Intersect_2" fill="#ffffff" d="M11.52975625 8.322454166666667c0.8049520833333333 -0.5059520833333334 1.7365479166666666 -1.0619770833333333 2.3505520833333335 -1.4243708333333334 0.41936666666666667 -0.24748958333333332 0.7040875000000001 -0.6909583333333333 0.6407416666666667 -1.1737666666666668 -0.21658333333333335 -1.6501445833333332 -1.3205354166666667 -3.015228125 -2.0240958333333334 -3.7353245833333335 -0.3524270833333334 -0.36072145833333336 -0.8847812500000001 -0.44720625 -1.3501479166666666 -0.25291854166666666 -0.9193770833333335 0.38380770833333333 -1.8125437500000001 0.8314883333333334 -2.6752833333333332 1.3302529166666668 0.5289041666666666 1.2604047916666667 1.55753125 3.4144027083333333 3.0582333333333334 5.2561277083333335Z" strokeWidth="1"></path>
											<path id="Rectangle 650 (Stroke)_2" fillRule="evenodd" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="M12.4967625 1.9889920833333337c-0.3524270833333334 -0.36072145833333336 -0.8847812500000001 -0.44720625 -1.3501479166666666 -0.25291854166666666 -1.499025 0.6257916666666667 -2.9284270833333337 1.4213904166666667 -4.269758333333334 2.3313614583333333C1.456719375 7.744531250000001 -0.798516875 13.722758333333333 4.281196041666667 18.849122916666666 9.196070833333334 23.80916875 15.464672916666666 21.388370833333333 18.934079166666667 16.2538125c0.9135312500000001 -1.3520166666666669 1.7240895833333334 -2.8197520833333334 2.3476291666666667 -4.346952083333333 0.18376041666666668 -0.45003333333333334 0.10431458333333334 -0.9629333333333333 -0.24375208333333337 -1.3023270833333336 -0.65075625 -0.6345125 -1.8894020833333334 -1.6141208333333332 -3.6165583333333333 -1.9810187499999998 -0.5312041666666667 -0.11284374999999999 -1.0528729166666666 0.16722916666666665 -1.3360604166666668 0.6305833333333334 -0.5349895833333334 0.8754375 -1.418525 2.305845833333333 -1.9957291666666668 3.1601041666666667 -1.6949083333333335 2.508341666666667 -4.031229166666667 3.5762125 -5.503468750000001 2.0904604166666667 -1.4722395833333333 -1.4858 -0.5372895833333333 -3.84804375 2.0336312500000004 -5.592162500000001 0.8812833333333333 -0.5979041666666667 2.3899395833333337 -1.5006541666666668 3.2603458333333335 -2.0144166666666665 0.41936666666666667 -0.24748958333333332 0.7040875000000001 -0.6909583333333333 0.6407416666666667 -1.1737666666666668 -0.21658333333333335 -1.6501445833333332 -1.3205354166666667 -3.015228125 -2.0240958333333334 -3.7353245833333335Z" clipRule="evenodd" strokeWidth="1"></path>
											<path id="Intersect_3" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="M19.84799375 14.800691666666667c-1.2941333333333334 -0.5798875 -3.4195729166666666 -1.6718604166666668 -5.194885416666667 -3.247504166666667m-3.1235916666666665 -3.2306375000000003c-1.5007020833333333 -1.8417729166666668 -2.529329166666667 -3.9957612499999997 -3.0582333333333334 -5.256166041666667" strokeWidth="1"></path>
										</g>
									</svg>
									<span className={"menu-btn-text"}>Steal Competitor Keywords</span>
								</div>

						}



						{pageData.current_plan_name === "Trial" ?
							<NavLink to={pageURL['manageSubscription']} className={"upgrade"} >
								<div className={"menu-btns Import-Keywords-using-CSV-btn"}>
									<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="-0.5 -0.5 24 24" id="Download-Box-2--Streamline-Plump" height="24" width="24">
										<g>
											<path id="Rectangle 1097" fill="#ffffff" d="M21.78085625 19.0218625C21.650858333333332 20.24162916666667 20.682702083333332 21.11285 19.459772916666665 21.205616666666668C17.863620833333332 21.328283333333335 15.264716666666667 21.45933541666667 11.5 21.45933541666667S5.136379166666667 21.32775625 3.5402270833333334 21.206143750000003C2.317297916666667 21.11285 1.3491416666666667 20.24162916666667 1.21914375 19.022389583333336C1.1106125 18.007035416666668 1.0162645833333332 16.570781250000003 1.0162645833333332 14.644914583333335C1.0162645833333332 12.719047916666668 1.1106125 11.282793750000002 1.21914375 10.267966666666666C1.3491416666666667 9.048152083333333 2.317297916666667 8.176979166666667 3.5402270833333334 8.084212500000001C5.136379166666667 7.961545833333334 7.735283333333334 7.83049375 11.5 7.83049375S17.863620833333332 7.962072916666666 19.459772916666665 8.083685416666667C20.682702083333332 8.176979166666667 21.650858333333332 9.048152083333333 21.78085625 10.267439583333335C21.8893875 11.282793750000002 21.98373541666667 12.719047916666668 21.98373541666667 14.644914583333335C21.98373541666667 16.570781250000003 21.8893875 18.007035416666668 21.78085625 19.0218625Z" strokeWidth="1"></path>
											<path id="hover" fill="#ffffff" d="M13.582075 10.996060416666667L13.365060416666667 2.9120395833333337C13.346708333333334 2.2353125 12.8938 1.643541666666667 12.220235416666666 1.5764104166666666C11.741452083333334 1.5287333333333333 11.259075 1.5287333333333333 10.780291666666667 1.5764104166666666C10.106200000000001 1.64406875 9.653291666666668 2.2353125 9.634939583333335 2.9125666666666667L9.417925 10.996060416666667C8.872010416666667 11.006841666666668 8.3262875 11.025529166666667 7.7809 11.052122916666667C6.745085416666667 11.104016666666666 6.31996875 12.084775 7.007189583333333 12.861120833333333C7.360479166666667 13.259500000000001 7.803420833333333 13.737564583333333 8.354366666666666 14.299483333333335C9.391187500000001 15.355710416666668 10.171175000000002 15.964300000000001 10.695383333333332 16.309731250000002C11.181258333333334 16.63901458333333 11.818741666666666 16.63901458333333 12.304616666666668 16.309731250000002C12.828825 15.964300000000001 13.6088125 15.355710416666668 14.645106250000001 14.299483333333335C15.106304166666668 13.831720833333334 15.555475000000001 13.352266666666667 15.992283333333333 12.861647916666668C16.67945625 12.084775 16.2543875 11.104016666666666 15.218045833333333 11.052122916666667C14.768827083333333 11.029602083333334 14.228902083333335 11.010195833333333 13.582075 10.996060416666667Z" strokeWidth="1"></path>
											<path id="Rectangle 1096" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="M16.217683333333333 7.909125C17.29892291666667 7.945685416666667 18.3793 8.00385625 19.458191666666668 8.083685416666667C20.681120833333335 8.176979166666667 21.65033125 9.048152083333333 21.78085625 10.267439583333335C21.8893875 11.282793750000002 21.98373541666667 12.719047916666668 21.98373541666667 14.6443875C21.98373541666667 16.570781250000003 21.8893875 18.007035416666668 21.78085625 19.022389583333336C21.650858333333332 20.24162916666667 20.682702083333332 21.11285 19.459772916666665 21.205616666666668C17.863620833333332 21.32722916666667 15.264716666666667 21.45933541666667 11.5 21.45933541666667S5.136379166666667 21.32722916666667 3.5402270833333334 21.205616666666668C2.317297916666667 21.11285 1.3491416666666667 20.24162916666667 1.21914375 19.022389583333336C1.1106125 18.007035416666668 1.0162645833333332 16.570781250000003 1.0162645833333332 14.644914583333335C1.0162645833333332 12.719047916666668 1.1106125 11.282793750000002 1.21914375 10.267966666666666C1.3491416666666667 9.048152083333333 2.318879166666667 8.176452083333334 3.5412812500000004 8.083685416666667C4.620364583333333 8.00385625 5.700933333333333 7.945685416666667 6.782316666666667 7.909125" strokeWidth="1"></path>
											<path id="Union_2" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="M13.582075 10.996060416666667L13.365060416666667 2.9120395833333337C13.346708333333334 2.2353125 12.8938 1.643541666666667 12.220235416666666 1.5764104166666666C11.741452083333334 1.5287333333333333 11.259075 1.5287333333333333 10.780291666666667 1.5764104166666666C10.106200000000001 1.64406875 9.653291666666668 2.2353125 9.634939583333335 2.9125666666666667L9.417925 10.996060416666667C8.872010416666667 11.006841666666668 8.3262875 11.025529166666667 7.7809 11.052122916666667C6.745085416666667 11.104016666666666 6.31996875 12.084775 7.007189583333333 12.861120833333333C7.360479166666667 13.259500000000001 7.803420833333333 13.737564583333333 8.354366666666666 14.299483333333335C9.391187500000001 15.355710416666668 10.171175000000002 15.964300000000001 10.695383333333332 16.309731250000002C11.181258333333334 16.63901458333333 11.818741666666666 16.63901458333333 12.304616666666668 16.309731250000002C12.828825 15.964300000000001 13.6088125 15.355710416666668 14.645106250000001 14.299483333333335C15.106304166666668 13.831720833333334 15.555475000000001 13.352266666666667 15.992283333333333 12.861647916666668C16.67945625 12.084775 16.2543875 11.104016666666666 15.218045833333333 11.052122916666667C14.768827083333333 11.029602083333334 14.228902083333335 11.010195833333333 13.582075 10.996060416666667Z" strokeWidth="1"></path>
										</g>
									</svg>
									<span className={"menu-btn-text"}>
										Import Keywords using CSV
										<svg xmlns="http://www.w3.org/2000/svg" viewBox="-0.5 -0.5 24 24" height="24" width="24">
											<g>
												<path d="M5.03125 9.34375h12.9375s1.4375 0 1.4375 1.4375v10.0625s0 1.4375 -1.4375 1.4375H5.03125s-1.4375 0 -1.4375 -1.4375v-10.0625s0 -1.4375 1.4375 -1.4375" fill="none" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1"></path>
												<path d="M6.46875 9.34375V5.75a5.03125 5.03125 0 0 1 10.0625 0v3.59375" fill="none" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1"></path>
												<path d="M9.34375 15.8125a2.15625 2.15625 0 1 0 4.3125 0 2.15625 2.15625 0 1 0 -4.3125 0" fill="none" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1"></path>
											</g>
										</svg>
									</span>
								</div>
							</NavLink>
							:
							<div className={"menu-btns Import-Keywords-using-CSV-btn"} onClick={() => handlePageChange("csv")}>
								<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="-0.5 -0.5 24 24" id="Download-Box-2--Streamline-Plump" height="24" width="24">
									<g>
										<path id="Rectangle 1097" fill="#ffffff" d="M21.78085625 19.0218625C21.650858333333332 20.24162916666667 20.682702083333332 21.11285 19.459772916666665 21.205616666666668C17.863620833333332 21.328283333333335 15.264716666666667 21.45933541666667 11.5 21.45933541666667S5.136379166666667 21.32775625 3.5402270833333334 21.206143750000003C2.317297916666667 21.11285 1.3491416666666667 20.24162916666667 1.21914375 19.022389583333336C1.1106125 18.007035416666668 1.0162645833333332 16.570781250000003 1.0162645833333332 14.644914583333335C1.0162645833333332 12.719047916666668 1.1106125 11.282793750000002 1.21914375 10.267966666666666C1.3491416666666667 9.048152083333333 2.317297916666667 8.176979166666667 3.5402270833333334 8.084212500000001C5.136379166666667 7.961545833333334 7.735283333333334 7.83049375 11.5 7.83049375S17.863620833333332 7.962072916666666 19.459772916666665 8.083685416666667C20.682702083333332 8.176979166666667 21.650858333333332 9.048152083333333 21.78085625 10.267439583333335C21.8893875 11.282793750000002 21.98373541666667 12.719047916666668 21.98373541666667 14.644914583333335C21.98373541666667 16.570781250000003 21.8893875 18.007035416666668 21.78085625 19.0218625Z" strokeWidth="1"></path>
										<path id="hover" fill="#ffffff" d="M13.582075 10.996060416666667L13.365060416666667 2.9120395833333337C13.346708333333334 2.2353125 12.8938 1.643541666666667 12.220235416666666 1.5764104166666666C11.741452083333334 1.5287333333333333 11.259075 1.5287333333333333 10.780291666666667 1.5764104166666666C10.106200000000001 1.64406875 9.653291666666668 2.2353125 9.634939583333335 2.9125666666666667L9.417925 10.996060416666667C8.872010416666667 11.006841666666668 8.3262875 11.025529166666667 7.7809 11.052122916666667C6.745085416666667 11.104016666666666 6.31996875 12.084775 7.007189583333333 12.861120833333333C7.360479166666667 13.259500000000001 7.803420833333333 13.737564583333333 8.354366666666666 14.299483333333335C9.391187500000001 15.355710416666668 10.171175000000002 15.964300000000001 10.695383333333332 16.309731250000002C11.181258333333334 16.63901458333333 11.818741666666666 16.63901458333333 12.304616666666668 16.309731250000002C12.828825 15.964300000000001 13.6088125 15.355710416666668 14.645106250000001 14.299483333333335C15.106304166666668 13.831720833333334 15.555475000000001 13.352266666666667 15.992283333333333 12.861647916666668C16.67945625 12.084775 16.2543875 11.104016666666666 15.218045833333333 11.052122916666667C14.768827083333333 11.029602083333334 14.228902083333335 11.010195833333333 13.582075 10.996060416666667Z" strokeWidth="1"></path>
										<path id="Rectangle 1096" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="M16.217683333333333 7.909125C17.29892291666667 7.945685416666667 18.3793 8.00385625 19.458191666666668 8.083685416666667C20.681120833333335 8.176979166666667 21.65033125 9.048152083333333 21.78085625 10.267439583333335C21.8893875 11.282793750000002 21.98373541666667 12.719047916666668 21.98373541666667 14.6443875C21.98373541666667 16.570781250000003 21.8893875 18.007035416666668 21.78085625 19.022389583333336C21.650858333333332 20.24162916666667 20.682702083333332 21.11285 19.459772916666665 21.205616666666668C17.863620833333332 21.32722916666667 15.264716666666667 21.45933541666667 11.5 21.45933541666667S5.136379166666667 21.32722916666667 3.5402270833333334 21.205616666666668C2.317297916666667 21.11285 1.3491416666666667 20.24162916666667 1.21914375 19.022389583333336C1.1106125 18.007035416666668 1.0162645833333332 16.570781250000003 1.0162645833333332 14.644914583333335C1.0162645833333332 12.719047916666668 1.1106125 11.282793750000002 1.21914375 10.267966666666666C1.3491416666666667 9.048152083333333 2.318879166666667 8.176452083333334 3.5412812500000004 8.083685416666667C4.620364583333333 8.00385625 5.700933333333333 7.945685416666667 6.782316666666667 7.909125" strokeWidth="1"></path>
										<path id="Union_2" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="M13.582075 10.996060416666667L13.365060416666667 2.9120395833333337C13.346708333333334 2.2353125 12.8938 1.643541666666667 12.220235416666666 1.5764104166666666C11.741452083333334 1.5287333333333333 11.259075 1.5287333333333333 10.780291666666667 1.5764104166666666C10.106200000000001 1.64406875 9.653291666666668 2.2353125 9.634939583333335 2.9125666666666667L9.417925 10.996060416666667C8.872010416666667 11.006841666666668 8.3262875 11.025529166666667 7.7809 11.052122916666667C6.745085416666667 11.104016666666666 6.31996875 12.084775 7.007189583333333 12.861120833333333C7.360479166666667 13.259500000000001 7.803420833333333 13.737564583333333 8.354366666666666 14.299483333333335C9.391187500000001 15.355710416666668 10.171175000000002 15.964300000000001 10.695383333333332 16.309731250000002C11.181258333333334 16.63901458333333 11.818741666666666 16.63901458333333 12.304616666666668 16.309731250000002C12.828825 15.964300000000001 13.6088125 15.355710416666668 14.645106250000001 14.299483333333335C15.106304166666668 13.831720833333334 15.555475000000001 13.352266666666667 15.992283333333333 12.861647916666668C16.67945625 12.084775 16.2543875 11.104016666666666 15.218045833333333 11.052122916666667C14.768827083333333 11.029602083333334 14.228902083333335 11.010195833333333 13.582075 10.996060416666667Z" strokeWidth="1"></path>
									</g>
								</svg>
								<span className={"menu-btn-text"}>Import Keywords using CSV</span>
							</div>
						}
						<div className={"menu-btns Manual-Add-Keywords-btn"} onClick={() => handlePageChange("manual")}>
							<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="-0.5 -0.5 24 24" id="Input-Box--Streamline-Plump" height="24" width="24">
								<desc>Input Box Streamline Icon: https://streamlinehq.com</desc>
								<g>
									<path id="Rectangle 1097" fill="#ffffff" d="M21.343999999999998 8.156374999999999c-0.1135625 -0.750375 -0.7484583333333333 -1.2554166666666668 -1.5065000000000002 -1.2913541666666666C18.409583333333334 6.7979375 15.709958333333335 6.708333333333334 11.5 6.708333333333334c-4.209958333333334 0 -6.909583333333334 0.09008333333333333 -8.3375 0.1566875 -0.7580416666666667 0.035458333333333335 -1.3929375000000002 0.5409791666666667 -1.5065000000000002 1.2913541666666666C1.5429166666666667 8.899083333333333 1.4375 9.997333333333334 1.4375 11.5c0 1.5026666666666668 0.10589583333333334 2.6013958333333336 0.21850000000000003 3.343625 0.1135625 0.750375 0.7484583333333333 1.2554166666666668 1.5065000000000002 1.2913541666666666 1.4279166666666667 0.06708333333333334 4.1275416666666676 0.1566875 8.3375 0.1566875 4.209958333333334 0 6.909583333333334 -0.09008333333333333 8.3375 -0.1566875 0.7580416666666667 -0.035458333333333335 1.3929375000000002 -0.5409791666666667 1.5065000000000002 -1.2913541666666666 0.11260416666666666 -0.7427083333333334 0.21850000000000003 -1.8409583333333335 0.21850000000000003 -3.343625 0 -1.5026666666666668 -0.10589583333333334 -2.6013958333333336 -0.21850000000000003 -3.343625Z" strokeWidth="1"></path>
									<path id="hover" fill="#ffffff" d="M11.494250000000001 3.3033750000000004c-0.015333333333333334 0.5347500000000001 -0.40920833333333334 0.917125 -0.9430000000000001 0.9497083333333334a29.267500000000002 29.267500000000002 0 0 1 -1.0445833333333334 0.043125c0.03833333333333334 1.3349583333333335 0.07666666666666667 3.5918333333333337 0.07666666666666667 7.2037916666666675 0 3.6124375 -0.0388125 5.869791666666667 -0.07666666666666667 7.2033125 0.4317291666666667 0.011020833333333334 0.7748125 0.026833333333333334 1.0445833333333334 0.043125 0.5337916666666668 0.03354166666666667 0.9276666666666666 0.4154375 0.9430000000000001 0.9501875000000001a15.218333333333335 15.218333333333335 0 0 1 0 0.85675c-0.015333333333333334 0.5347500000000001 -0.40920833333333334 0.9166458333333334 -0.9430000000000001 0.9497083333333334 -0.5079166666666667 0.031625 -1.2769791666666668 0.059416666666666666 -2.4054166666666665 0.059416666666666666 -1.1279583333333334 0 -1.8975 -0.028270833333333332 -2.4054166666666665 -0.059416666666666666 -0.5337916666666668 -0.0330625 -0.9276666666666666 -0.4149583333333333 -0.9430000000000001 -0.9497083333333334a15.218333333333335 15.218333333333335 0 0 1 0 -0.85675c0.015333333333333334 -0.5347500000000001 0.40920833333333334 -0.9166458333333334 0.9430000000000001 -0.9497083333333334a29.243062500000004 29.243062500000004 0 0 1 1.0445833333333334 -0.043125C6.746666666666667 17.36883333333333 6.708333333333334 15.111958333333334 6.708333333333334 11.5c0 -3.6124375 0.0388125 -5.869791666666667 0.07666666666666667 -7.2033125a29.267500000000002 29.267500000000002 0 0 1 -1.0445833333333334 -0.043125c-0.5337916666666668 -0.03354166666666667 -0.9276666666666666 -0.4154375 -0.9430000000000001 -0.9501875000000001a15.217854166666667 15.217854166666667 0 0 1 0 -0.85675c0.015333333333333334 -0.5347500000000001 0.40920833333333334 -0.917125 0.9430000000000001 -0.9497083333333334C6.248333333333333 1.4652916666666667 7.0173958333333335 1.4375 8.145833333333334 1.4375c1.1284375 0 1.8975 0.02779166666666667 2.4054166666666665 0.059416666666666666 0.5337916666666668 0.03258333333333334 0.9276666666666666 0.4149583333333333 0.9430000000000001 0.9497083333333334a15.217854166666667 15.217854166666667 0 0 1 0 0.85675Z" strokeWidth="1"></path>
									<path id="Rectangle 1096" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="M11.979166666666668 6.7088125c3.6565208333333334 0.006229166666666667 6.116083333333333 0.0805 7.547354166666667 0.1423125 0.9482708333333334 0.04120833333333333 1.7384166666666667 0.6732291666666667 1.8610833333333334 1.6143125000000003 0.09487500000000001 0.7268958333333333 0.17489583333333333 1.7283541666666669 0.17489583333333333 3.0345625000000003 0 1.3066875 -0.08002083333333335 2.3076666666666665 -0.17489583333333333 3.0345625000000003 -0.12266666666666667 0.9410833333333334 -0.9128125 1.5731041666666667 -1.8610833333333334 1.6147916666666668 -1.4312708333333335 0.061812500000000006 -3.890833333333333 0.13608333333333333 -7.547354166666667 0.14183333333333334" strokeWidth="1"></path>
									<path id="Rectangle 1098" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="M4.3125 6.8180625c-0.31145833333333334 0.011020833333333334 -0.5908125000000001 0.022041666666666668 -0.8390208333333333 0.0330625 -0.9482708333333334 0.04120833333333333 -1.7384166666666667 0.6732291666666667 -1.8610833333333334 1.6143125000000003C1.5175208333333332 9.192333333333334 1.4375 10.193312500000001 1.4375 11.5c0 1.3066875 0.08002083333333335 2.3076666666666665 0.17489583333333333 3.0345625000000003 0.12266666666666667 0.9410833333333334 0.9128125 1.5731041666666667 1.8610833333333334 1.6143125000000003 0.24820833333333336 0.011020833333333334 0.5275625 0.022041666666666668 0.8390208333333333 0.0330625" strokeWidth="1"></path>
									<path id="Union_2" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="M11.494250000000001 3.3033750000000004c-0.015333333333333334 0.5347500000000001 -0.40920833333333334 0.917125 -0.9430000000000001 0.9497083333333334a29.267500000000002 29.267500000000002 0 0 1 -1.0445833333333334 0.043125c0.03833333333333334 1.3349583333333335 0.07666666666666667 3.5918333333333337 0.07666666666666667 7.2037916666666675 0 3.6124375 -0.0388125 5.869791666666667 -0.07666666666666667 7.2033125 0.4317291666666667 0.011020833333333334 0.7748125 0.026833333333333334 1.0445833333333334 0.043125 0.5337916666666668 0.03354166666666667 0.9276666666666666 0.4154375 0.9430000000000001 0.9501875000000001a15.218333333333335 15.218333333333335 0 0 1 0 0.85675c-0.015333333333333334 0.5347500000000001 -0.40920833333333334 0.9166458333333334 -0.9430000000000001 0.9497083333333334 -0.5079166666666667 0.031625 -1.2769791666666668 0.059416666666666666 -2.4054166666666665 0.059416666666666666 -1.1279583333333334 0 -1.8975 -0.028270833333333332 -2.4054166666666665 -0.059416666666666666 -0.5337916666666668 -0.0330625 -0.9276666666666666 -0.4149583333333333 -0.9430000000000001 -0.9497083333333334a15.218333333333335 15.218333333333335 0 0 1 0 -0.85675c0.015333333333333334 -0.5347500000000001 0.40920833333333334 -0.9166458333333334 0.9430000000000001 -0.9497083333333334a29.243062500000004 29.243062500000004 0 0 1 1.0445833333333334 -0.043125C6.746666666666667 17.36883333333333 6.708333333333334 15.111958333333334 6.708333333333334 11.5c0 -3.6124375 0.0388125 -5.869791666666667 0.07666666666666667 -7.2033125a29.267500000000002 29.267500000000002 0 0 1 -1.0445833333333334 -0.043125c-0.5337916666666668 -0.03354166666666667 -0.9276666666666666 -0.4154375 -0.9430000000000001 -0.9501875000000001a15.217854166666667 15.217854166666667 0 0 1 0 -0.85675c0.015333333333333334 -0.5347500000000001 0.40920833333333334 -0.917125 0.9430000000000001 -0.9497083333333334C6.248333333333333 1.4652916666666667 7.0173958333333335 1.4375 8.145833333333334 1.4375c1.1284375 0 1.8975 0.02779166666666667 2.4054166666666665 0.059416666666666666 0.5337916666666668 0.03258333333333334 0.9276666666666666 0.4149583333333333 0.9430000000000001 0.9497083333333334a15.217854166666667 15.217854166666667 0 0 1 0 0.85675Z" strokeWidth="1"></path>
								</g>
							</svg>
							<span className={"menu-btn-text"}>Manual Add Keywords</span>
						</div>
						{pageData.current_plan_name === "Trial" ?
							<NavLink to={pageURL['manageSubscription']} className={"upgrade"} >
								<div className={"menu-btns GSC-Keywords-btn"}>
									<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="-0.5 -0.5 24 24" height="24" width="24">
										<g>
											<path id="Rectangle 1097" fill="#ffffff" d="M1.6957708333333334 19.043520833333336c0.09966666666666667 1.2242708333333334 1.0364375 2.1610416666666667 2.2607083333333335 2.2607083333333335C5.503229166666666 21.429770833333333 7.971416666666666 21.5625 11.5 21.5625c3.5285833333333336 0 5.996770833333334 -0.13272916666666668 7.543520833333334 -0.25827083333333334 1.2242708333333334 -0.09966666666666667 2.1610416666666667 -1.0364375 2.2607083333333335 -2.2607083333333335C21.429770833333333 17.496770833333333 21.5625 15.028583333333334 21.5625 11.5c0 -3.5285833333333336 -0.13272916666666668 -5.996770833333334 -0.25827083333333334 -7.543520833333334 -0.09966666666666667 -1.2242708333333334 -1.0364375 -2.1610416666666667 -2.2607083333333335 -2.2607083333333335C17.496770833333333 1.5702291666666668 15.028583333333334 1.4375 11.5 1.4375c-3.5285833333333336 0 -5.996770833333334 0.13272916666666668 -7.543520833333334 0.25827083333333334 -1.2242708333333334 0.09966666666666667 -2.1610416666666667 1.0364375 -2.2607083333333335 2.2607083333333335C1.5702291666666668 5.503229166666666 1.4375 7.971416666666666 1.4375 11.5c0 3.5285833333333336 0.13272916666666668 5.996770833333334 0.25827083333333334 7.543520833333334Z" strokeWidth="1"></path>
											<path id="hover" fill="#ffffff" d="M21.459 6.46875H1.5414791666666667c0.04552083333333334 -1.0143958333333334 0.100625 -1.8481458333333336 0.1542916666666667 -2.5122708333333335 0.09966666666666667 -1.2242708333333334 1.0364375 -2.1610416666666667 2.2611875 -2.2607083333333335C5.503708333333334 1.5702291666666668 7.971895833333334 1.4375 11.5 1.4375c3.5285833333333336 0 5.996770833333334 0.13272916666666668 7.543520833333334 0.25827083333333334 1.22475 0.09966666666666667 2.1610416666666667 1.0364375 2.2611875 2.2607083333333335 0.05366666666666667 0.664125 0.10877083333333334 1.497875 0.1542916666666667 2.5122708333333335Z" strokeWidth="1"></path>
											<path id="Vector 1456" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="M5.75 11.020833333333334s3.8333333333333335 1.9166666666666667 3.8333333333333335 2.875 -3.8333333333333335 2.875 -3.8333333333333335 2.875" strokeWidth="1"></path>
											<path id="Vector 1462" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="M12.458333333333334 16.291666666666668h4.791666666666667" strokeWidth="1"></path>
											<path id="Rectangle 1096" stroke="#2859c5" strokeLinejoin="round" d="M1.6957708333333334 19.043520833333336c0.09966666666666667 1.2242708333333334 1.0364375 2.1610416666666667 2.2607083333333335 2.2607083333333335C5.503229166666666 21.429770833333333 7.971416666666666 21.5625 11.5 21.5625c3.5285833333333336 0 5.996770833333334 -0.13272916666666668 7.543520833333334 -0.25827083333333334 1.2242708333333334 -0.09966666666666667 2.1610416666666667 -1.0364375 2.2607083333333335 -2.2607083333333335C21.429770833333333 17.496770833333333 21.5625 15.028583333333334 21.5625 11.5c0 -3.5285833333333336 -0.13272916666666668 -5.996770833333334 -0.25827083333333334 -7.543520833333334 -0.09966666666666667 -1.2242708333333334 -1.0364375 -2.1610416666666667 -2.2607083333333335 -2.2607083333333335C17.496770833333333 1.5702291666666668 15.028583333333334 1.4375 11.5 1.4375c-3.5285833333333336 0 -5.996770833333334 0.13272916666666668 -7.543520833333334 0.25827083333333334 -1.2242708333333334 0.09966666666666667 -2.1610416666666667 1.0364375 -2.2607083333333335 2.2607083333333335C1.5702291666666668 5.503229166666666 1.4375 7.971416666666666 1.4375 11.5c0 3.5285833333333336 0.13272916666666668 5.996770833333334 0.25827083333333334 7.543520833333334Z" strokeWidth="1"></path>
											<path id="Vector 146" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="m1.6770833333333335 6.46875 19.645833333333336 0" strokeWidth="1"></path>
											<path id="Vector 1501" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="M4.791666666666667 4.072916666666667h0.9583333333333334" strokeWidth="1"></path>
											<path id="Vector 1502" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="M8.625 4.072916666666667h0.9583333333333334" strokeWidth="1"></path>
										</g>
									</svg>
									<span className={"menu-btn-text"} style={{ fontSize: "15px" }}>
										Import Keywords from GSC
										<svg xmlns="http://www.w3.org/2000/svg" viewBox="-0.5 -0.5 24 24" height="24" width="24">
											<g>
												<path d="M5.03125 9.34375h12.9375s1.4375 0 1.4375 1.4375v10.0625s0 1.4375 -1.4375 1.4375H5.03125s-1.4375 0 -1.4375 -1.4375v-10.0625s0 -1.4375 1.4375 -1.4375" fill="none" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1"></path>
												<path d="M6.46875 9.34375V5.75a5.03125 5.03125 0 0 1 10.0625 0v3.59375" fill="none" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1"></path>
												<path d="M9.34375 15.8125a2.15625 2.15625 0 1 0 4.3125 0 2.15625 2.15625 0 1 0 -4.3125 0" fill="none" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1"></path>
											</g>
										</svg>
									</span>
								</div>
							</NavLink>
							:
							<div className={"menu-btns GSC-Keywords-btn"} onClick={() => handlePageChange("gsc")}>
								<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="-0.5 -0.5 24 24" height="24" width="24">
									<g>
										<path id="Rectangle 1097" fill="#ffffff" d="M1.6957708333333334 19.043520833333336c0.09966666666666667 1.2242708333333334 1.0364375 2.1610416666666667 2.2607083333333335 2.2607083333333335C5.503229166666666 21.429770833333333 7.971416666666666 21.5625 11.5 21.5625c3.5285833333333336 0 5.996770833333334 -0.13272916666666668 7.543520833333334 -0.25827083333333334 1.2242708333333334 -0.09966666666666667 2.1610416666666667 -1.0364375 2.2607083333333335 -2.2607083333333335C21.429770833333333 17.496770833333333 21.5625 15.028583333333334 21.5625 11.5c0 -3.5285833333333336 -0.13272916666666668 -5.996770833333334 -0.25827083333333334 -7.543520833333334 -0.09966666666666667 -1.2242708333333334 -1.0364375 -2.1610416666666667 -2.2607083333333335 -2.2607083333333335C17.496770833333333 1.5702291666666668 15.028583333333334 1.4375 11.5 1.4375c-3.5285833333333336 0 -5.996770833333334 0.13272916666666668 -7.543520833333334 0.25827083333333334 -1.2242708333333334 0.09966666666666667 -2.1610416666666667 1.0364375 -2.2607083333333335 2.2607083333333335C1.5702291666666668 5.503229166666666 1.4375 7.971416666666666 1.4375 11.5c0 3.5285833333333336 0.13272916666666668 5.996770833333334 0.25827083333333334 7.543520833333334Z" strokeWidth="1"></path>
										<path id="hover" fill="#ffffff" d="M21.459 6.46875H1.5414791666666667c0.04552083333333334 -1.0143958333333334 0.100625 -1.8481458333333336 0.1542916666666667 -2.5122708333333335 0.09966666666666667 -1.2242708333333334 1.0364375 -2.1610416666666667 2.2611875 -2.2607083333333335C5.503708333333334 1.5702291666666668 7.971895833333334 1.4375 11.5 1.4375c3.5285833333333336 0 5.996770833333334 0.13272916666666668 7.543520833333334 0.25827083333333334 1.22475 0.09966666666666667 2.1610416666666667 1.0364375 2.2611875 2.2607083333333335 0.05366666666666667 0.664125 0.10877083333333334 1.497875 0.1542916666666667 2.5122708333333335Z" strokeWidth="1"></path>
										<path id="Vector 1456" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="M5.75 11.020833333333334s3.8333333333333335 1.9166666666666667 3.8333333333333335 2.875 -3.8333333333333335 2.875 -3.8333333333333335 2.875" strokeWidth="1"></path>
										<path id="Vector 1462" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="M12.458333333333334 16.291666666666668h4.791666666666667" strokeWidth="1"></path>
										<path id="Rectangle 1096" stroke="#2859c5" strokeLinejoin="round" d="M1.6957708333333334 19.043520833333336c0.09966666666666667 1.2242708333333334 1.0364375 2.1610416666666667 2.2607083333333335 2.2607083333333335C5.503229166666666 21.429770833333333 7.971416666666666 21.5625 11.5 21.5625c3.5285833333333336 0 5.996770833333334 -0.13272916666666668 7.543520833333334 -0.25827083333333334 1.2242708333333334 -0.09966666666666667 2.1610416666666667 -1.0364375 2.2607083333333335 -2.2607083333333335C21.429770833333333 17.496770833333333 21.5625 15.028583333333334 21.5625 11.5c0 -3.5285833333333336 -0.13272916666666668 -5.996770833333334 -0.25827083333333334 -7.543520833333334 -0.09966666666666667 -1.2242708333333334 -1.0364375 -2.1610416666666667 -2.2607083333333335 -2.2607083333333335C17.496770833333333 1.5702291666666668 15.028583333333334 1.4375 11.5 1.4375c-3.5285833333333336 0 -5.996770833333334 0.13272916666666668 -7.543520833333334 0.25827083333333334 -1.2242708333333334 0.09966666666666667 -2.1610416666666667 1.0364375 -2.2607083333333335 2.2607083333333335C1.5702291666666668 5.503229166666666 1.4375 7.971416666666666 1.4375 11.5c0 3.5285833333333336 0.13272916666666668 5.996770833333334 0.25827083333333334 7.543520833333334Z" strokeWidth="1"></path>
										<path id="Vector 146" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="m1.6770833333333335 6.46875 19.645833333333336 0" strokeWidth="1"></path>
										<path id="Vector 1501" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="M4.791666666666667 4.072916666666667h0.9583333333333334" strokeWidth="1"></path>
										<path id="Vector 1502" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="M8.625 4.072916666666667h0.9583333333333334" strokeWidth="1"></path>
									</g>
								</svg>
								<span className={"menu-btn-text"}>
									Import Keywords from GSC
								</span>
							</div>
						}
						{pageData.current_plan_name === "Trial" ?
							<NavLink to={pageURL['manageSubscription']} className={"upgrade"} >
								<div className={"menu-btns longtail-keyword-research-btn"}>
									<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="-0.5 -0.5 24 24" height="24" width="24">
										<g id="auto-flash">
											<path id="hover" fill="#ffffff" d="M5.896385416666667 12.194360416666667C4.646047916666666 12.016014583333334 3.4079291666666665 11.760283333333334 2.189216666666667 11.428604166666666C1.2701270833333334 11.174502083333334 0.9505229166666667 10.118418750000002 1.5237020833333335 9.355633333333333C2.9063375000000002 7.515441666666667 5.435379166666667 4.275747916666666 8.19763125 1.3507229166666668C8.961902083333333 0.5427520833333334 10.252202083333334 1.1273833333333332 10.16786875 2.2365583333333334C9.993164583333334 4.507520833333333 9.7718375 6.245458333333334 9.61845625 7.775485416666667C11.001475 7.9749625 12.363889583333334 8.297633333333334 13.689408333333333 8.739760416666666C14.557418750000002 9.032052083333333 14.828866666666666 10.049945833333334 14.27998125 10.782447916666667C12.912775 12.604766666666666 10.345016666666668 15.905027083333335 7.5356145833333334 18.879214583333336C6.77235 19.68814375 5.467627083333333 19.10250625 5.5197125 17.991366666666664C5.63140625 15.621120833333334 5.742045833333333 13.735791666666668 5.896385416666667 12.194839583333334Z" strokeWidth="1"></path>
											<path id="Vector 1" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="M13.822425 22.025566666666666L14.881 19.04792916666667M14.881 19.04792916666667L16.74749791666667 13.798802083333333C16.89738125 13.376991666666667 17.318185416666665 13.09260625 17.79265625 13.09260625S18.687931250000002 13.376991666666667 18.837814583333333 13.798802083333333L20.70378541666667 19.04792916666667M14.881 19.04792916666667H20.704312500000004M20.704312500000004 19.04792916666667L21.762839583333335 22.025566666666666" strokeWidth="1"></path>
											<path id="Union_2" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="M5.896385416666667 12.194360416666667C4.646047916666666 12.016014583333334 3.4079291666666665 11.760283333333334 2.189216666666667 11.428604166666666C1.2701270833333334 11.174502083333334 0.9505229166666667 10.118418750000002 1.5237020833333335 9.355633333333333C2.9063375000000002 7.515441666666667 5.435379166666667 4.275747916666666 8.19763125 1.3507229166666668C8.961902083333333 0.5427520833333334 10.252202083333334 1.1273833333333332 10.16786875 2.2365583333333334C9.993164583333334 4.507520833333333 9.7718375 6.245458333333334 9.61845625 7.775485416666667C11.001475 7.9749625 12.363889583333334 8.297633333333334 13.689408333333333 8.739760416666666C14.557418750000002 9.032052083333333 14.828866666666666 10.049945833333334 14.27998125 10.782447916666667C12.912775 12.604766666666666 10.345016666666668 15.905027083333335 7.5356145833333334 18.879214583333336C6.77235 19.68814375 5.467627083333333 19.10250625 5.5197125 17.991366666666664C5.63140625 15.621120833333334 5.742045833333333 13.735791666666668 5.896385416666667 12.194839583333334Z" strokeWidth="1"></path>
										</g>
									</svg>
									<span className={"menu-btn-text"}>
										Longtail Keyword Research
										<span className="uncollapsed-tag tag is-info is-light is-rounded mr-2">
											New ⚡
										</span>
										<svg xmlns="http://www.w3.org/2000/svg" viewBox="-0.5 -0.5 24 24" height="24" width="24" className={"longtail-tail-lock-icon"}>
											<g>
												<path d="M5.03125 9.34375h12.9375s1.4375 0 1.4375 1.4375v10.0625s0 1.4375 -1.4375 1.4375H5.03125s-1.4375 0 -1.4375 -1.4375v-10.0625s0 -1.4375 1.4375 -1.4375" fill="none" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1"></path>
												<path d="M6.46875 9.34375V5.75a5.03125 5.03125 0 0 1 10.0625 0v3.59375" fill="none" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1"></path>
												<path d="M9.34375 15.8125a2.15625 2.15625 0 1 0 4.3125 0 2.15625 2.15625 0 1 0 -4.3125 0" fill="none" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1"></path>
											</g>
										</svg>
									</span>
								</div>
							</NavLink>
							:
							<div className={"menu-btns longtail-keyword-research-btn"} onClick={() => handlePageChange("longtail")}>
								<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="-0.5 -0.5 24 24" height="24" width="24">
									<g id="auto-flash">
										<path id="hover" fill="#ffffff" d="M5.896385416666667 12.194360416666667C4.646047916666666 12.016014583333334 3.4079291666666665 11.760283333333334 2.189216666666667 11.428604166666666C1.2701270833333334 11.174502083333334 0.9505229166666667 10.118418750000002 1.5237020833333335 9.355633333333333C2.9063375000000002 7.515441666666667 5.435379166666667 4.275747916666666 8.19763125 1.3507229166666668C8.961902083333333 0.5427520833333334 10.252202083333334 1.1273833333333332 10.16786875 2.2365583333333334C9.993164583333334 4.507520833333333 9.7718375 6.245458333333334 9.61845625 7.775485416666667C11.001475 7.9749625 12.363889583333334 8.297633333333334 13.689408333333333 8.739760416666666C14.557418750000002 9.032052083333333 14.828866666666666 10.049945833333334 14.27998125 10.782447916666667C12.912775 12.604766666666666 10.345016666666668 15.905027083333335 7.5356145833333334 18.879214583333336C6.77235 19.68814375 5.467627083333333 19.10250625 5.5197125 17.991366666666664C5.63140625 15.621120833333334 5.742045833333333 13.735791666666668 5.896385416666667 12.194839583333334Z" strokeWidth="1"></path>
										<path id="Vector 1" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="M13.822425 22.025566666666666L14.881 19.04792916666667M14.881 19.04792916666667L16.74749791666667 13.798802083333333C16.89738125 13.376991666666667 17.318185416666665 13.09260625 17.79265625 13.09260625S18.687931250000002 13.376991666666667 18.837814583333333 13.798802083333333L20.70378541666667 19.04792916666667M14.881 19.04792916666667H20.704312500000004M20.704312500000004 19.04792916666667L21.762839583333335 22.025566666666666" strokeWidth="1"></path>
										<path id="Union_2" stroke="#2859c5" strokeLinecap="round" strokeLinejoin="round" d="M5.896385416666667 12.194360416666667C4.646047916666666 12.016014583333334 3.4079291666666665 11.760283333333334 2.189216666666667 11.428604166666666C1.2701270833333334 11.174502083333334 0.9505229166666667 10.118418750000002 1.5237020833333335 9.355633333333333C2.9063375000000002 7.515441666666667 5.435379166666667 4.275747916666666 8.19763125 1.3507229166666668C8.961902083333333 0.5427520833333334 10.252202083333334 1.1273833333333332 10.16786875 2.2365583333333334C9.993164583333334 4.507520833333333 9.7718375 6.245458333333334 9.61845625 7.775485416666667C11.001475 7.9749625 12.363889583333334 8.297633333333334 13.689408333333333 8.739760416666666C14.557418750000002 9.032052083333333 14.828866666666666 10.049945833333334 14.27998125 10.782447916666667C12.912775 12.604766666666666 10.345016666666668 15.905027083333335 7.5356145833333334 18.879214583333336C6.77235 19.68814375 5.467627083333333 19.10250625 5.5197125 17.991366666666664C5.63140625 15.621120833333334 5.742045833333333 13.735791666666668 5.896385416666667 12.194839583333334Z" strokeWidth="1"></path>
									</g>
								</svg>
								<span className={"menu-btn-text"}>
									Longtail Keyword Research
									<span className="uncollapsed-tag tag is-info is-light is-rounded mr-2">
										New ⚡
									</span>
								</span>
							</div>
						}
					</div>
					<hr style={{ width: '100%', height: '2px', border: 'none', borderBottom: '2px solid rgb(219, 219, 219)' }} />
					<div className={"table-container"}>
						{showSurvey && <Survey />}
						<AbunTable
							ref={tableRef}
							serverSide={true}
							apiUrl="/api/frontend/get-keyword-projects/"
							tableContentName={"Keyword Projects"}
							tableName="Your Keywords Project"
							tableDescription="Choose your Keyword Project & Generate Articles for Keywords within it."
							columnDefs={columnDefs}
							tableSpace="fixed"
							pageSizes={pageSizes}
							initialPageSize={pageSizes[0]}
							noDataText={"No keywords data available."}
							searchboxPlaceholderText={"Search keywords projects..."}
							transformResponse={(rawData) => ({
								data: rawData.keyword_projects.map((project: any) => ({
									projectName: project.projectName,
									totalKeywords: project.totalKeywords,
									totalTrafficVolume: project.totalTrafficVolume,
									dateCreated: project.dateCreated,
									projectId: project.projectId,
									locationIsoCode: project.locationIsoCode,
									mostRecentArtTitleTimestamp: project.mostRecentArtTitleTimestamp,
								})),
								total: rawData.total
							})}
							handleRowClick={(row, event) => {
								if (event?.ctrlKey || event?.metaKey) {
									// open the kw project in a new tab
									openUrlInNewTab(`/keyword-research/?keywordProjectId=${row.projectId}`);
								} else {
									setSelectedTableRow(row);
									setSelectedPage("keyword-project-page");
								}
							}}
						/>
					</div>
				</div>
			}



			{
				selectedPage === "keyword-project-page" &&
				<div className={"keyword-project-container is-flex w-100 is-align-items-center is-flex-direction-column"}>
					<div className={"keyword-project-header"}>
						<svg className={"back-btn"} onClick={handleBackBtnClick} stroke="#bcbcbc" fill="#bcbcbc" width="28" height="24">
							<path d="M27.5 12H2M2 12L13 1M2 12L13 23" stroke="black" strokeOpacity="0.5" strokeWidth="2" />
						</svg>
					</div>
					{
						selectedTableRow &&
						<LazyLoadKeywordProject
							projectName={selectedTableRow?.projectName || ""}
							totalKeywords={selectedTableRow?.totalKeywords || 0}
							totalTrafficVolume={selectedTableRow?.totalTrafficVolume || 0}
							projectId={selectedTableRow?.projectId || ""}
							locationIsoCode={selectedTableRow?.locationIsoCode || 'US'}
							icpKeywordProject={(selectedTableRow?.projectId?.startsWith('icp-') || selectedTableRow?.projectName?.startsWith('ICP-Keyword')) ?? false}
							failAlertRef={failAlertRef}
							successAlertRef={successAlertRef}
							setSelectedKeywordRow={setSelectedKeywordRow}
							setSelectedPage={setSelectedPage}
							mostRecentArtTitleTimestamp={selectedTableRow?.mostRecentArtTitleTimestamp || ""}
							dateCreated={selectedTableRow?.dateCreated || ""}
						/>
					}
				</div>
			}

			{
				selectedPage === "AI-keyword-research" &&
				<div className={"ai-keyword-research-container is-flex w-100 is-justify-content-flex-start is-flex-direction-column"}>
					<Helmet>
						<title>AI Keyword Research | Abun.com</title>
						<meta
						  name="description"
						  content="Discover high-impact keywords for your niche using AI analysis."
						/>
					</Helmet>

					<div className={"ai-keyword-research-header is-flex is-justify-content-center is-flex-direction-column"}>
						<h2> AI Keyword Research </h2>
						<p className="has-text-dark is-size-5">Let AI do the heavy lifting instantly generate smart keyword based on your seed keywords.<br />
							This tool finds high-value keywords with ranking potential, so you can focus on creating content that drives traffic.
						</p>
					</div>

					<div className="tabs is-medium" style={{scrollbarWidth:'none'}}>
                        <ul>
                          <li className={activeAITab === "ai-keywords" ? "is-active" : ""}>
                            <a onClick={() => setActiveAITab("ai-keywords")}>AI Keyword Research</a>
                          </li>
                          <li className={activeAITab === "projects" ? "is-active" : ""}>
                            <a onClick={() => setActiveAITab("projects")}>Projects</a>
                          </li>
                        </ul>
                    </div>


                    {activeAITab === "ai-keywords" &&
					<div className={"longtail-keywords-container"}>
						<h3>Add seed keywords to begin keyword research</h3>
						<hr/>
					<div className={"ai-keyword-research-content"}>
						<form className={"ai-keyword-research-form"}>
							<div className={"form-group location-select"}>
								<Autocomplete
									id="ai-keyword-research-location-select"
									sx={{ width: 200 }}
									options={countries}
									value={selectedLocation}
									autoHighlight
									getOptionLabel={(option) => option.country_iso_code !== "ZZ" ? `${option.location_name} (${option.country_iso_code})` : option.location_name}
									isOptionEqualToValue={(option, value) => option.location_code === value.location_code}
									renderOption={(props, option) => (
										<Box component="li" sx={{ '& > img': { mr: 2, flexShrink: 0 } }} {...props}>
											<img
												loading="lazy"
												width="20"
												srcSet={option.country_iso_code !== "ZZ" ? `https://flagcdn.com/w40/${option.country_iso_code.toLowerCase()}.png 2x` : EarthFlag}
												src={option.country_iso_code !== "ZZ" ? `https://flagcdn.com/w20/${option.country_iso_code.toLowerCase()}.png` : EarthFlag}
												alt=""
											/>
											{option.location_name} ({option.country_iso_code})
										</Box>
									)}
									renderInput={(params) => (
										<TextField
											{...params}
											label="Location"
											inputProps={{
												...params.inputProps,
												// disable autocomplete and autofill and suggestion
												autoComplete: 'off',
											}}
										/>
									)}
									onChange={(_event, option) => {
										if (option) {
											setSelectedLocation(option);
										}
									}}
								/>
							</div>

							{inputKeywords.map((keyword, index) => (
								<div key={index} className={`form-group ${index >= 1 ? "extras" : ""}`}>
									{index >= 1 && <div className={"balancer remove-extra-keyword"} />}
									<TextField id="outlined-basic" label={inputKeywords.length === 1 ? "Seed Keyword" : ` Seed Keyword ${index + 1}`} variant="outlined" sx={{ width: 375 }} value={keyword} onChange={(e) => handleKeywordChange(e.target.value, index)} />
									{index >= 1 && (
										<button
											type="button"
											className="remove-extra-keyword"
											onClick={() => removeKeywordField(index)}
										>&times;</button>
									)}
								</div>
							))}
							<div className="form-group add-more">
								<button
									type="button"
									className={`add-more-keywords ${aiKeywordsResearchApi.isLoading || !inputKeywords[0] || inputKeywords.length >= 50
										? "disabled" : ""}`}
									onClick={addKeywordField}
								>
									+ Add More
								</button>
							</div>
						</form>

						<GenericButton
							text={aiKeywordsResearchApi.isLoading ? "Generating..." : "PROCEED"}
							type="none"
							iconAfterText={"arrow-right"}
							iconWidth={"1.1em"}
							disable={aiKeywordsResearchApi.isLoading || !inputKeywords[0]}
							additionalClassList={["mt-2", "is-responsive is-link", "is-size-6" ]}
							clickHandler={aiKeywordsResearch}
						/>
					</div>
					</div>
					}

					{activeAITab === "projects" &&
                        <div className={"Longtail-keywords-table"}>
							{aiKeywordsResearchApi.isLoading ? 
							<div>
								<div className={"w-100 is-flex is-justify-content-center is-align-items-center"}>
                                    <AbunLoader show={aiKeywordsResearchApi.isLoading} height="20vh" />
                                </div>
							</div>
							: 
					        <AbunTable
							    ref={tableRef}
					            serverSide={true}
					            apiUrl="/api/frontend/get-keyword-projects/"
					            tableContentName="AI keywords"
					            columnDefs={columnDefs}
					            tableSpace="fixed"
					            pageSizes={pageSizes}
					            initialPageSize={pageSizes[0]}
					            noDataText={"No keywords data available."}
					            searchboxPlaceholderText={"Search longtail keywords..."}
                                transformResponse={(rawData) => {
                                	const aiKeywords = rawData.keyword_projects.filter((project: any) =>
                                		project.projectName?.toLowerCase().includes("ai-keyword")
                                	);
                                
                                	return {
                                		data: aiKeywords.map((project: any) => ({
                                			projectName: project.projectName,
                                			totalKeywords: project.totalKeywords,
                                			totalTrafficVolume: project.totalTrafficVolume,
                                			dateCreated: project.dateCreated,
                                			projectId: project.projectId,
                                			locationIsoCode: project.locationIsoCode,
                                			mostRecentArtTitleTimestamp: project.mostRecentArtTitleTimestamp,
                                		})),
                                		total: aiKeywords.length,
                                	};
                                }}
					            handleRowClick={(row, event) => {
					            	if (event?.ctrlKey || event?.metaKey) {
					            		// open the kw project in a new tab
					            		openUrlInNewTab(`/keyword-research/?keywordProjectId=${row.projectId}`);
					            	} else {
					            		setSelectedTableRow(row);
					            		setSelectedPage("keyword-project-page");
					            	}
					            }}
					        />}
					    </div>
                    }
				</div>
			}


			{
				selectedPage === "longtail-keyword" && (
					<>
					    <Helmet>
						    <title>Longtail Keyword Research | Abun.com</title>
						    <meta
						      name="description"
						      content="Generate low-competition, high-conversion longtail keywords."
						    />
					    </Helmet>
						{pageData.current_plan_name === "Trial" && <div className="trial-blur-overlay"></div>}
						<div className="trial-blur-wrapper">
							<div className={`ai-keyword-research-container is-flex w-100 is-flex-direction-column ${pageData.current_plan_name === "Trial" ? "trial-blur-content" : ""}`}>
								
								<div className="ai-keyword-research-header is-flex is-justify-content-center is-flex-direction-column">
									<h2 className="">Longtail Keyword Research</h2>
									<p className="has-text-dark is-size-5">Easily find high-intent, low-competition keywords your audience is actually searching for. <br/>
									      Use this tool to uncover long-tail keyword ideas that are easier to rank for and bring in more targeted traffic.
									</p>
								</div>


                                <div className="tabs is-medium" style={{scrollbarWidth:'none'}}>
                                    <ul>
                                      <li className={activeLongtailTab === "longtail-keywords" ? "is-active" : ""}>
                                        <a onClick={() => setActiveLongtailTab("longtail-keywords")}>Longtail Keyword Research</a>
                                      </li>
                                      <li className={activeLongtailTab === "projects" ? "is-active" : ""}>
                                        <a onClick={() => setActiveLongtailTab("projects")}>Projects</a>
                                      </li>
                                    </ul>
                                </div>

                                {activeLongtailTab === "longtail-keywords" &&
								    <div className={"longtail-keywords-container"}>
										<h3 >Add seed keywords to begin keyword research </h3>
                                        <hr/>
								    	<LazyTextAreaUpload
								    		addKeywordsDone={addKeywordsDone}
								    		failAlertRef={failAlertRef}
								    		successAlertRef={successAlertRef}
								    		addKeywordsFailed={addKeywordsFailed}
								    		googleSuggestionsEnabled={true}
								    		countryCode={pageData.country_code}
								    		selectedPageName="Longtail Keywords"
								    		isEnableOnTrialPlan={false}
								    		onGetSuggestions={async (keyword: string) => {
								    			if (!keyword) return [];
								    			try {
								    				const res = await getLongtailKeywordSuggestions(keyword);
								    				return res.data.keywords;
								    			} catch (err) {
								    				console.error('Failed to get longtail suggestions:', err);
								    				return [];
								    			}
								    		}}
								    	/>
								    </div>
								}

							    {activeLongtailTab === "projects" &&
                                    <div className={"Longtail-keywords-table"}>
							            <AbunTable
										    ref={tableRef}
							                serverSide={true}
							                apiUrl="/api/frontend/get-keyword-projects/"
							                tableContentName="Longtail Keywords"
							                columnDefs={columnDefs}
							                tableSpace="fixed"
							                pageSizes={pageSizes}
							                initialPageSize={pageSizes[0]}
							                noDataText={"No keywords data available."}
							                searchboxPlaceholderText={"Search longtail keywords..."}
                                            transformResponse={(rawData) => {
                                            	const longtailKeywords = rawData.keyword_projects.filter((project: any) =>
                                            		project.projectName.toLowerCase().includes("longtail keywords")
                                            	);
                                            
                                            	return {
                                            		data: longtailKeywords.map((project: any) => ({
                                            			projectName: project.projectName,
                                            			totalKeywords: project.totalKeywords,
                                            			totalTrafficVolume: project.totalTrafficVolume,
                                            			dateCreated: project.dateCreated,
                                            			projectId: project.projectId,
                                            			locationIsoCode: project.locationIsoCode,
                                            			mostRecentArtTitleTimestamp: project.mostRecentArtTitleTimestamp,
                                            		})),
                                            		total: longtailKeywords.length,
                                            	};
                                            }}
							                handleRowClick={(row, event) => {
							                	if (event?.ctrlKey || event?.metaKey) {
							                		// open the kw project in a new tab
							                		openUrlInNewTab(`/keyword-research/?keywordProjectId=${row.projectId}`);
							                	} else {
							                		setSelectedTableRow(row);
							                		setSelectedPage("keyword-project-page");
							                	}
							                }}
						                />
						            </div>
                                }
							</div>

							{/* Upgrade Modal */}
							{
								!hamburgerActive && pageData.current_plan_name === "Trial" && (
									<div className="longtail-upgrade-modal">
										<h3>Upgrade to Unlock Longtail Keywords.</h3>
										<p>Upgrade your plan to access all features.</p>
										<button onClick={() => navigate(pageURL['manageSubscription'])}>
											See Plans →
										</button>
									</div>
								)}
						</div>
					</>
				)
			}

			{
				selectedPage === "Import-Keywords-using-CSV" && (
					<>
					    <Helmet>
						    <title>Bulk Keyword to Article | Abun.com</title>
						    <meta
						      name="description"
						      content="Upload multiple keywords and auto-generate SEO articles in bulk."
						    />
					    </Helmet>
						{pageData.current_plan_name === "Trial" && <div className="trial-blur-overlay"></div>}

						<div className="trial-blur-wrapper">
							<div className={`import-keywords-using-csv-container is-flex w-100 is-align-items-center is-justify-content-space-between is-flex-direction-column ${pageData.current_plan_name === "Trial" ? "trial-blur-content" : ""}`}>
								<div className="ai-keyword-research-header">
									<svg className="back-btn" onClick={handleBackBtnClick} stroke="#bcbcbc" fill="#bcbcbc" width="28" height="24" viewBox="0 0 28 24">
										<path d="M27.5 12H2M2 12L13 1M2 12L13 23" stroke="black" strokeOpacity="0.5" strokeWidth="2" />
									</svg>
									<h2 className="has-text-centered">
										Import Keywords using CSV
									</h2>
									<svg className="back-btn balance" onClick={handleBackBtnClick} stroke="#bcbcbc" fill="#bcbcbc" width="28" height="24" viewBox="0 0 28 24">
										<path d="M27.5 12H2M2 12L13 1M2 12L13 23" stroke="black" strokeOpacity="0.5" strokeWidth="2" />
									</svg>
								</div>

								<LazyCSVUpload
									addKeywordsDone={addKeywordsDone}
									failAlertRef={failAlertRef}
									successAlertRef={successAlertRef}
									countryCode={pageData.country_code}
								/>
							</div>

							{!hamburgerActive && pageData.current_plan_name === "Trial" && (
								<div className="csv-upgrade-modal">
									<h3>Upgrade to Unlock Bulk Keywords.</h3>
									<p>Upgrade your plan to access all features.</p>
									<button onClick={() => navigate(pageURL['manageSubscription'])}>
										See Plans →
									</button>
								</div>
							)}
						</div>
					</>
				)}


			{selectedPage === "Import-GSC-Keywords" && (
				<>
				    <Helmet>
						<title>GSC Keyword to Article | Abun.com</title>
						<meta
						  name="description"
						  content="Turn your Google Search Console keywords into ranking-ready articles."
						/>
					</Helmet>
					{pageData.current_plan_name === "Trial" && <div className="trial-blur-overlay"></div>}

					<div className="trial-blur-wrapper">
						<div className={`import-gsc-keywords-container is-flex w-100 is-align-items-center is-flex-direction-column ${pageData.current_plan_name === "Trial" ? "trial-blur-content" : ""}`}>
							<div className="ai-keyword-research-header">
								<svg className="back-btn" onClick={handleBackBtnClick} stroke="#bcbcbc" fill="#bcbcbc" width="28" height="24" viewBox="0 0 28 24">
									<path d="M27.5 12H2M2 12L13 1M2 12L13 23" stroke="black" strokeOpacity="0.5" strokeWidth="2" />
								</svg>
								<h2 className=" has-text-centered">
									Import Keywords from Google Search Console
								</h2>
								<svg className="back-btn balance" onClick={handleBackBtnClick} stroke="#bcbcbc" fill="#bcbcbc" width="28" height="24" viewBox="0 0 28 24">
									<path d="M27.5 12H2M2 12L13 1M2 12L13 23" stroke="black" strokeOpacity="0.5" strokeWidth="2" />
								</svg>
							</div>

							{pageData.has_gsc_integration ? (
								!selectedDomain ? (
									<LazyLoadGSCDomainsList
										failAlertRef={failAlertRef}
										setSelectedDomain={setSelectedDomain}
										handleBackBtnClick={handleBackBtnClick}
									/>
								) : (
									<GSCKeywords2
										selectedDomain={selectedDomain}
										updatePageData={() => { }}
										handleBackBtnClick={() => {
											setSelectedTableRow(null);
											setSelectedDomain("");
										}}
										failAlertRef={failAlertRef}
										successAlertRef={successAlertRef}
										countryCode={pageData.country_code}
									/>
								)
							) : (
								<LazyLoadGSCIntegration failAlertRef={failAlertRef} />
							)}
						</div>

						{!hamburgerActive && pageData.current_plan_name === "Trial" && (
							<div className="gsc-upgrade-modal">
								<h3>Upgrade to Unlock GSC Keywords.</h3>
								<p>Upgrade your plan to access all features.</p>
								<button onClick={() => navigate(pageURL['manageSubscription'])}>
									See Plans →
								</button>
							</div>
						)}
					</div>
				</>
			)}


			{
				selectedPage === "Manual-Add-Keywords" &&
				<div className={"manual-add-keywords-container ai-keyword-research-container is-flex w-100 is-flex-direction-column"}>
					<Helmet>
						<title>Manual Keyword to Article | Abun.com</title>
						<meta
						  name="description"
						  content="Paste your own keyword and generate a full article tailored to it."
						/>
					</Helmet>

					<div className={"ai-keyword-research-header is-flex is-justify-content-center is-flex-direction-column"}>
						<h2>Manual Keyword Research</h2>
						<p className="has-text-dark is-size-5">Search any keyword, check its search volume & difficulty.<br/>
							Instantly generate an AI-powered article for your keywords.
						</p>
					</div>

                    <div className="tabs is-medium" style={{scrollbarWidth:'none'}}>
                        <ul>
                          <li className={activeManualTab === "manual-keywords" ? "is-active" : ""}>
                            <a onClick={() => setActiveManualTab("manual-keywords")}>Manual Keyword Research</a>
                          </li>
                          <li className={activeManualTab === "projects" ? "is-active" : ""}>
                            <a onClick={() => setActiveManualTab("projects")}>Projects</a>
                          </li>
                        </ul>
                    </div>

                    {activeManualTab === "manual-keywords" &&
					    <div className={"longtail-keywords-container"}>
							<h3 >Add keywords to get search volume, difficulty & turn them into AI Aritcle</h3>
                            <hr/>
					        <LazyTextAreaUpload
					        	addKeywordsDone={addKeywordsDone}
					        	failAlertRef={failAlertRef}
					        	successAlertRef={successAlertRef}
					        	addKeywordsFailed={addKeywordsFailed}
					        	countryCode={pageData.country_code}
					        	googleSuggestionsEnabled={false}
					        	isEnableOnTrialPlan={true}
					        	selectedPageName="Manual Keywords Upload"
								setActiveTab={setActiveManualTab}
					        />
						</div>
                    }

					{activeManualTab === "projects" &&
                        <div className={"Longtail-keywords-table"}>
					        <AbunTable
							    ref={tableRef}
					            serverSide={true}
					            apiUrl="/api/frontend/get-keyword-projects/"
					            tableContentName="Manual Keywords"
					            columnDefs={columnDefs}
					            tableSpace="fixed"
					            pageSizes={pageSizes}
					            initialPageSize={pageSizes[0]}
					            noDataText={"No keywords data available."}
					            searchboxPlaceholderText={"Search longtail keywords..."}
                                transformResponse={(rawData) => {
                                	const manualKeywords = rawData.keyword_projects.filter((project: any) =>
                                		project.projectName.toLowerCase().includes("manual keywords") || project.projectName.toLowerCase().includes("csv keywords")
                                	);
                                
                                	return {
                                		data: manualKeywords.map((project: any) => ({
                                			projectName: project.projectName,
                                			totalKeywords: project.totalKeywords,
                                			totalTrafficVolume: project.totalTrafficVolume,
                                			dateCreated: project.dateCreated,
                                			projectId: project.projectId,
                                			locationIsoCode: project.locationIsoCode,
                                			mostRecentArtTitleTimestamp: project.mostRecentArtTitleTimestamp,
                                		})),
                                		total: manualKeywords.length,
                                	};
                                }}
					            handleRowClick={(row, event) => {
					            	if (event?.ctrlKey || event?.metaKey) {
					            		// open the kw project in a new tab
					            		openUrlInNewTab(`/keyword-research/?keywordProjectId=${row.projectId}`);
					            	} else {
					            		setSelectedTableRow(row);
					            		setSelectedPage("keyword-project-page");
					            	}
					            }}
					        />
					    </div>
                    }

				</div>
			}

			{
				selectedPage === "Icp-Keywords" &&
				<div className={"manual-add-keywords-container ai-keyword-research-container is-flex w-100 is-flex-direction-column"}>
					<Helmet>
						<title>Create ICP to keyword | Abun.com</title>
						<meta
						  name="description"
						  content="Paste your own keyword and generate a full article tailored to it."
						/>
					</Helmet>

					<div className={"ai-keyword-research-header is-flex is-justify-content-center is-flex-direction-column"}>
						<h2>ICP to Keyword Research</h2>
						<p className="has-text-dark is-size-5">Turn your ideal Customer Profile into actionable keywords.<br/>
						Just describe your target audience, and AI will generate relevant, intent-driven keywords they're likely to search.<br/>
						Find keywords your ICP is searching for to target better.
						</p>
					</div>

                    <div className="tabs is-medium" style={{scrollbarWidth:'none'}}>
                        <ul>
                          <li className={activeICPTab === "Icp-Keywords" ? "is-active" : ""}>
                            <a onClick={() => setActiveICPTab("Icp-Keywords")}>ICP to Keyword Research</a>
                          </li>
                          <li className={activeICPTab === "projects" ? "is-active" : ""}>
                            <a onClick={() => setActiveICPTab("projects")}>Projects</a>
                          </li>
                        </ul>
                    </div>

                    {activeICPTab === "Icp-Keywords" &&
					    <div className={"longtail-keywords-container"}>
							<h3 >Describe your ideal customers & get keywords they actually search.</h3>
                            <hr/>
							<IcpToKeyword />
						</div>
                    }

					{activeICPTab === "projects" &&
                        <div className={"Longtail-keywords-table"}>
					        <AbunTable
					            serverSide={true}
					            apiUrl="/api/frontend/get-keyword-projects/"
					            tableContentName="ICP Keywords"
					            columnDefs={columnDefs}
					            tableSpace="fixed"
					            pageSizes={pageSizes}
					            initialPageSize={pageSizes[0]}
					            noDataText={"No keywords data available."}
					            searchboxPlaceholderText={"Search ICP keywords..."}
                                transformResponse={(rawData) => {
                                	const manualKeywords = rawData.keyword_projects.filter((project: any) =>
                                		project.projectName.toLowerCase().includes("icp-")
                                	);
                                
                                	return {
                                		data: manualKeywords.map((project: any) => ({
                                			projectName: project.projectName,
                                			totalKeywords: project.totalKeywords,
                                			totalTrafficVolume: project.totalTrafficVolume,
                                			dateCreated: project.dateCreated,
                                			projectId: project.projectId,
                                			locationIsoCode: project.locationIsoCode,
                                			mostRecentArtTitleTimestamp: project.mostRecentArtTitleTimestamp,
                                		})),
                                		total: manualKeywords.length,
                                	};
                                }}
					            handleRowClick={(row, event) => {
					            	if (event?.ctrlKey || event?.metaKey) {
					            		// open the kw project in a new tab
					            		openUrlInNewTab(`/keyword-research/?keywordProjectId=${row.projectId}`);
					            	} else {
					            		setSelectedTableRow(row);
					            		setSelectedPage("keyword-project-page");
					            	}
					            }}
					        />
					    </div>
                    }

				</div>
			}

			{
				showConnectWebsiteWarningModal &&
				<div className={"modal is-active"}>
					<div className={"modal-background"}></div>
					<div className={"modal-content"}>
						<div className={"modal-card"}>
							<header className={"modal-card-head"}>
								<p className={"modal-card-title"}>Connect Your Website</p>
								<button type="button" className={"delete"} aria-label="close" onClick={() => setShowConnectWebsiteWarningModal(false)}></button>
							</header>
							<section className={"modal-card-body"}>
								<p>
									Please connect your website to get the competition analysis for your website.
								</p>
							</section>
							<footer className={"modal-card-foot is-justify-content-center is-align-items-center"}>
								<AbunButton type={"primary"} clickHandler={() => {
									setShowConnectWebsiteWarningModal(false);
									setShowConnectWebsiteModal(true);
								}}>Connect Website</AbunButton>
							</footer>
						</div>
					</div>
				</div>
			}

			{
				showConnectWebsiteModal && <ConnectWebsite
					setShowConnectWebsiteModal={setShowConnectWebsiteModal}
					successAlertRef={successAlertRef}
					failAlertRef={failAlertRef}
					navigateOrReload='navigate' />
			}
			<AbunModal
				active={showDeletePopUp}
				headerText="Confirm Deletion"
				closeable={true}
				hideModal={() => setShowDeletePopUp(false)}
				modelWidth='700px'
			>
				<div className={"has-text-centered"}>
					<p>Are you sure you want to delete the keyword Project: <strong>{deleteKwProjectName}</strong>?</p>
					<p>Note: All associated keywords and titles for this project will also be deleted.</p>
					<AbunButton type={"danger"}
						className={"mt-4 "}
						disabled={removeKeywordProject.isLoading}
						clickHandler={() => {
							setShowDeletePopUp(false);
							removeKeywordProject.mutate({
								projectId: deleteKwProjectId
							}, {
								onSuccess: () => {
									tableRef.current?.refetchData().then(() => {
										successAlertRef.current?.show("keyword project removed successfully!");
										setTimeout(() => {
											successAlertRef.current?.close();
										}, 5000);
									})
									setShowDeletePopUp(false); // Close the modal after deletion
								},
								onError: () => {
									failAlertRef.current?.show("Failed to remove keyword project. Please try again after some time.");
									setTimeout(() => {
										failAlertRef.current?.close();
									}, 5000);
									setShowDeletePopUp(false); // Close the modal after deletion
								}
							})
						}
						}>
						Proceed
					</AbunButton>
				</div>
			</AbunModal>
			<SuccessAlert ref={successAlertRef} />
			<ErrorAlert ref={failAlertRef} />
		</>
	)
}
