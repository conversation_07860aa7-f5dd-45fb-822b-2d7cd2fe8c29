import { Autocomplete, Box, TextField } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import { RowData, RowModel, Row } from "@tanstack/react-table";
import { useEffect, useRef, useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from "../../../components/Icon/Icon";
import {
    createCustomKeywordMutation,
    GenerateTitlesFromKeyword,
    getKeywordProjectData,
    getTaskProgress,
    removeKeywordMutation,
    getkwTitlesMutation,
    fetchVolumeMutation,
} from "../../../utils/api";
import { CreateCustomKeywordModal, DeleteKeywordModal } from './KeywordProjectModals';
import { KeywordProjectTable } from './KeywordProjectTable';
import countries from '../../../utils/constants/CountriesforSerp';
import EarthFlag from '../../../assets/images/earth-flag.webp';
import { DeleteData, KeywordsProjectData, KeywordsProjectTableProps, CountryType } from './KeywordProjectTypes';
import { AbunTableRef } from "../../../components/AbunTable/AbunTable"
import AbunButton from "../../../components/AbunButton/AbunButton";
import AbunLoader from "../../../components/AbunLoader/AbunLoader";

export default function LoadKeywordProject(props: KeywordsProjectData) {
    const pageSizes = [5, 10, 15, 30, 50, 100, 500, 1000, 1500, 2000];
    const navigate = useNavigate();

    // Format total traffic volume
    let totalTrafficVolume = "" + props.totalTrafficVolume;
    if (props.totalTrafficVolume > 999 && props.totalTrafficVolume < 1000000) {
        totalTrafficVolume = (props.totalTrafficVolume / 1000).toFixed(2) + "K";
    } else if (props.totalTrafficVolume > 999999 && props.totalTrafficVolume < 1000000000) {
        totalTrafficVolume = (props.totalTrafficVolume / 1000000).toFixed(2) + "M";
    } else if (props.totalTrafficVolume > 999999999) {
        totalTrafficVolume = (props.totalTrafficVolume / 1000000000).toFixed(2) + "B";
    }

    // ------------------ TABLE DATA ------------------
    const [tableData, setTableData] = useState<Array<KeywordsProjectTableProps>>([]);
    const [selectedTab, setSelectedTab] = useState("All Keywords");
    const [selectedTabInfo, setSelectedTabInfo] = useState("Show all keywords");
    const [total_very_easy_keywords, setTotalVeryEasyKeywords] = useState(0);
    const [total_easy_keywords, setTotalEasyKeywords] = useState(0);
    const [total_moderate_keywords, setTotalModerateKeywords] = useState(0);
    const [totalKeywords, setTotalKeywords] = useState(props.totalKeywords);
    const [showCreateCustomkeywordModal, setShowCreateCustomkeywordModal] = useState(false);
    const [deleteData, setDeleteData] = useState<DeleteData | null>(null);
    const [showDeletePopUp, setShowDeletePopUp] = useState(false);
    const [customKeyword, setCustomKeyword] = useState("");
    const hasStartedPolling = useRef({});

    const [total_hard_keywords, setTotalHardKeywords] = useState(0);
    const [total_very_hard_keywords, setTotalVeryHardKeywords] = useState(0);
    const [titleGenProgressMap, setTitleGenProgressMap] = useState<Record<string, number>>({});
    const [titleGenProgressMessageMap, setTitleGenProgressMessageMap] = useState<Record<string, string>>({});
    const tableRef = useRef<AbunTableRef>(null);
    const taskPollingIntervals = useRef<Record<string, NodeJS.Timeout>>({});
    const [selectedRows, setSelectedRows] = useState<KeywordsProjectTableProps[]>([]);
    const [generatingVolume, setGeneratingVolume] = useState<Record<string, boolean>>({});
    const [selectedLocation, setSelectedLocation] = useState<CountryType>({
        location_code: 1,
        location_name: 'Global',
        country_iso_code: 'ZZ',
    });
    const allBulkOptions = useMemo(() => [
        {
            text: "Generate Volume",
            key: "generate"
        }

    ], []);
    const [availableBulkActions, setAvailableBulkActions] = useState(allBulkOptions);
    const [bulkActionsEnabled, setBulkActionsEnabled] = useState(true);
    const [isIcpLoading, SetIsIcpLoading] = useState(false)
    const [kwVolumeCount, setKwVolumeCount] = useState(0)

    // ----------------------- QUERIES -----------------------
    const { data, isLoading, isFetching, refetch } = useQuery({
        queryKey: ['getKeywordProjectData'],
        queryFn: () => getKeywordProjectData(props.projectId || ""),
        cacheTime: 0,
        refetchOnWindowFocus: false
    });

    const titlesForKeywordMutation = useMutation(GenerateTitlesFromKeyword);
    const getkwTitles = useMutation(getkwTitlesMutation)
    const createCustomKeyword = useMutation(createCustomKeywordMutation);
    const removeKeyword = useMutation(removeKeywordMutation);
    const fetchVolumeApi = useMutation(fetchVolumeMutation)

    // ----------------------- EFFECTS -----------------------
    useEffect(() => {
        if (data) {
            const keywordData = data?.data.keywords as Array<KeywordsProjectTableProps>;
            setKwVolumeCount(data?.data.kwVolumeCount)
            if (keywordData) {
                let TableDataToSet = keywordData;
                setTotalVeryEasyKeywords(keywordData.filter((keyword) => keyword.keywordTraffic && keyword.keywordTraffic < 1000).length);
                setTotalEasyKeywords(keywordData.filter((keyword) => keyword.keywordTraffic && keyword.keywordTraffic > 1000 && keyword.keywordTraffic < 15000).length);
                setTotalModerateKeywords(keywordData.filter((keyword) => keyword.keywordTraffic && keyword.keywordTraffic > 15000 && keyword.keywordTraffic < 35000).length);
                setTotalHardKeywords(keywordData.filter((keyword) => keyword.keywordTraffic && keyword.keywordTraffic > 35000 && keyword.keywordTraffic < 100000).length);
                setTotalVeryHardKeywords(keywordData.filter((keyword) => keyword.keywordTraffic && keyword.keywordTraffic > 100000).length);

                if (selectedTab === "Very Easy Keywords") {
                    TableDataToSet = keywordData.filter((keyword) => keyword.keywordTraffic && keyword.keywordTraffic < 1000);
                } else if (selectedTab === "Easy Keywords") {
                    TableDataToSet = keywordData.filter((keyword) => keyword.keywordTraffic && keyword.keywordTraffic > 1000 && keyword.keywordTraffic < 15000);
                } else if (selectedTab === "Moderate Keywords") {
                    TableDataToSet = keywordData.filter((keyword) => keyword.keywordTraffic && keyword.keywordTraffic > 15000 && keyword.keywordTraffic < 35000);
                } else if (selectedTab === "Hard Keywords") {
                    TableDataToSet = keywordData.filter((keyword) => keyword.keywordTraffic && keyword.keywordTraffic > 35000 && keyword.keywordTraffic < 100000);
                } else if (selectedTab === "Very Hard Keywords") {
                    TableDataToSet = keywordData.filter((keyword) => keyword.keywordTraffic && keyword.keywordTraffic > 100000);
                }

                // Sort table data by keyword traffic
                TableDataToSet.sort((a, b) => {
                    if (a.keywordTraffic && b.keywordTraffic) {
                        return b.keywordTraffic - a.keywordTraffic;
                    } else {
                        return 0;
                    }
                });

                // sort table data by mostRecentArtTitleTimestamp
                TableDataToSet.sort((a: KeywordsProjectTableProps, b: KeywordsProjectTableProps) => {
                    if (a.mostRecentArtTitleTimestamp && b.mostRecentArtTitleTimestamp) {
                        const timestampDiff = parseFloat(b.mostRecentArtTitleTimestamp) - parseFloat(a.mostRecentArtTitleTimestamp);
                        if (timestampDiff !== 0) return timestampDiff;
                    } else if (!a.mostRecentArtTitleTimestamp && !b.mostRecentArtTitleTimestamp) {
                        return 0;
                    } else if (!a.mostRecentArtTitleTimestamp) {
                        return 1;
                    } else {
                        return -1;
                    }

                    // Final fallback: kwVolume === true should come first
                    const kwVolumeA = a.kwVolume === true ? 0 : 1;
                    const kwVolumeB = b.kwVolume === true ? 0 : 1;
                    return kwVolumeA - kwVolumeB;
                });

                setTableData(TableDataToSet);
            }
        }
    }, [selectedTab]);

    useEffect(() => {
        const projectId = props.projectId;
        const storedTaskId = localStorage.getItem(`task_${projectId}`);
        if (storedTaskId && !hasStartedPolling.current[projectId]) {
            hasStartedPolling.current[projectId] = true;
            SetIsIcpLoading(true);
            pollIcpTaskProgress(storedTaskId, projectId);
        }
    }, [props.icpKeywordProject, props.projectId]);


    useEffect(() => {
        if (props.icpKeywordProject && selectedRows.length) {
            const hasNotGeneratedVolume = selectedRows.some(row => !row.kwVolume);

            const filteredActions = allBulkOptions.filter(option => {
                if (option.key === "generate" && hasNotGeneratedVolume) return true;
                return false;
            });

            setAvailableBulkActions(filteredActions);
        } else {
            setAvailableBulkActions([]);
        }
    }, [props.icpKeywordProject, selectedRows, allBulkOptions]);


    const fetchKwTitles = async () => {
        const response = await getkwTitles.mutateAsync(props.projectId);
        const tabledata = response.data.keywords;
        setKwVolumeCount(response.data.kwVolumeCount)
        if (tabledata) {
            const keywordData = tabledata as Array<KeywordsProjectTableProps>;
            if (keywordData) {
                let TableDataToSet = keywordData;
                // Sort table data by keyword traffic
                TableDataToSet.sort((a, b) => {
                    if (a.keywordTraffic && b.keywordTraffic) {
                        return b.keywordTraffic - a.keywordTraffic;
                    } else {
                        return 0;
                    }
                });

                // sort table data by mostRecentArtTitleTimestamp
                TableDataToSet.sort((a: KeywordsProjectTableProps, b: KeywordsProjectTableProps) => {
                    if (a.mostRecentArtTitleTimestamp && b.mostRecentArtTitleTimestamp) {
                        const timestampDiff = parseFloat(b.mostRecentArtTitleTimestamp) - parseFloat(a.mostRecentArtTitleTimestamp);
                        if (timestampDiff !== 0) return timestampDiff;
                    } else if (!a.mostRecentArtTitleTimestamp && !b.mostRecentArtTitleTimestamp) {
                        return 0;
                    } else if (!a.mostRecentArtTitleTimestamp) {
                        return 1;
                    } else {
                        return -1;
                    }

                    // Final fallback: kwVolume === true should come first
                    const kwVolumeA = a.kwVolume === true ? 0 : 1;
                    const kwVolumeB = b.kwVolume === true ? 0 : 1;
                    return kwVolumeA - kwVolumeB;
                });

                setTableData(TableDataToSet);
            }
        }
    }

    useEffect(() => {
        if (data) {
            const keywordData = data?.data.keywords as Array<KeywordsProjectTableProps>;
            setKwVolumeCount(data?.data.kwVolumeCount)
            if (keywordData) {
                let TableDataToSet = keywordData;
                // Sort table data by keyword traffic
                TableDataToSet.sort((a, b) => {
                    if (a.keywordTraffic && b.keywordTraffic) {
                        return b.keywordTraffic - a.keywordTraffic;
                    } else {
                        return 0;
                    }
                });

                // sort table data by mostRecentArtTitleTimestamp
                TableDataToSet.sort((a: KeywordsProjectTableProps, b: KeywordsProjectTableProps) => {
                    if (a.mostRecentArtTitleTimestamp && b.mostRecentArtTitleTimestamp) {
                        const timestampDiff = parseFloat(b.mostRecentArtTitleTimestamp) - parseFloat(a.mostRecentArtTitleTimestamp);
                        if (timestampDiff !== 0) return timestampDiff;
                    } else if (!a.mostRecentArtTitleTimestamp && !b.mostRecentArtTitleTimestamp) {
                        return 0;
                    } else if (!a.mostRecentArtTitleTimestamp) {
                        return 1;
                    } else {
                        return -1;
                    }

                    // Final fallback: kwVolume === true should come first
                    const kwVolumeA = a.kwVolume === true ? 0 : 1;
                    const kwVolumeB = b.kwVolume === true ? 0 : 1;
                    return kwVolumeA - kwVolumeB;
                });

                setTableData(TableDataToSet);
            }
        }
    }, [data]);

    useEffect(() => {
        const url = new URL(window.location.href);
        // do not delete keywordProjectId and keywordHash if both are present
        if (url.searchParams.get('keywordProjectId') && !url.searchParams.get('keywordHash')) {
            url.searchParams.delete('keywordProjectId');
            window.history.pushState({}, '', url.toString());
        }

        // On component mount, retrieve all stored tasks from localStorage
        const storedTasks = Object.keys(localStorage).filter(key => key.startsWith('titleGenTask-'));

        storedTasks.forEach(key => {
            const keywordHash = key.replace('titleGenTask-', '');
            const taskId = localStorage.getItem(key);

            // Resume polling for each stored task
            if (taskId) {
                pollTaskProgress(taskId, keywordHash);
            }
        });

        // Cleanup on unmount
        return () => {
            // Clear all intervals when component unmounts
            Object.values(taskPollingIntervals.current).forEach(interval => {
                clearInterval(interval);
            });
        };
    }, []);

    // ----------------------- FUNCTIONS -----------------------
    const startTitleGenTask = (keywordHash: string) => {
        // Start the task and store task ID in localStorage
        titlesForKeywordMutation.mutate({
            keyword_hash: keywordHash,
            location: props.locationIsoCode.toLowerCase()
        }, {
            onSuccess: (info) => {
                if (info.data.status == "rejected") {
                    if (info.data.reason == "max_limit_reached") {
                        props.failAlertRef.current?.show("Max limit reached to generate titles.");
                    } else {
                        props.failAlertRef.current?.show(`Titles generation request failed. Error ID: ${info.data.reason}`);
                    }
                    return;
                }
                if (info.data.task_id) {
                    // Store task ID in localStorage
                    localStorage.setItem(`titleGenTask-${keywordHash}`, info.data.task_id);

                    // Continue with the progress updates as before
                    pollTaskProgress(info.data.task_id, keywordHash);
                }
            },
            onError: (error) => {
                // Handle error
                console.error(`Error starting title generation task: ${error}`);
            }
        });
    };

    function handleFetchVolume(
        keywordHash: string | string[],
        selectedLocation: CountryType
    ) {
        const isSingle = typeof keywordHash === 'string';
        const keywordHashIds = isSingle ? [keywordHash] : keywordHash;

        if (isSingle) {
            setGeneratingVolume((prev) => ({ ...prev, [keywordHash]: true }));
        }

        fetchVolumeApi.mutate(
            { keywordHashIds, selectedLocation },
            {
                onSuccess: (response) => {
                    if (response.data.status === "success") {
                        const message = isSingle
                            ? "Successfully fetched volume for the keyword."
                            : "Successfully fetched volumes for selected keywords.";

                        // Clear selection to uncheck all checkboxes
                        setSelectedRows([]);
                        tableRef.current?.resetSelection?.();

                        // Hide bulk actions by clearing them manually (optional)
                        setAvailableBulkActions([]);

                        tableRef.current?.refetchData()
                        fetchKwTitles()
                        props.successAlertRef.current?.show(message);
                    } else if (response.data.status === "rejected") {
                        props.failAlertRef.current?.show("Max Limit reached to generate volume.");
                    } else {
                        throw new Error("Failed to fetch volumes.");
                    }
                },
                onError: () => {
                    const errorMessage = isSingle
                        ? "Failed to fetch volume for the keyword. Please try again."
                        : "Failed to fetch volumes for selected keywords. Please try again.";
                    props.failAlertRef.current?.show(errorMessage);
                    setTimeout(() => {
                        props.failAlertRef.current?.close();
                    }, 5000);
                },
                onSettled: () => {
                    if (isSingle) {
                        setGeneratingVolume((prev) => {
                            const newState = { ...prev };
                            delete newState[keywordHash as string];
                            return newState;
                        });
                    }
                },
            }
        );
    }

    const pollTaskProgress = (taskId: string, keywordHash: string) => {
        // Poll for task progress every 2 seconds
        taskPollingIntervals.current[keywordHash] = setInterval(() => {
            getTaskProgress(taskId).then((res) => {
                if (res.data.progress_info) {
                    setTitleGenProgressMap(prev => ({ ...prev, [keywordHash]: res.data.progress_info.progress.percent }));
                    setTitleGenProgressMessageMap((prev: Record<string, string>) => ({ ...prev, [keywordHash]: res.data.progress_info.progress.description }));

                    if (res.data.progress_info.progress.percent === 100) {
                        // Task is complete
                        clearInterval(taskPollingIntervals.current[keywordHash]);
                        tableRef.current?.refetchData()
                        fetchKwTitles()
                        setTimeout(() => {
                            // Refetch data and show success alert                            

                            props.successAlertRef.current?.show(
                                'New Titles have been generated successfully. Click here to visit',
                                {
                                    nextStepLinkText: 'Titles',
                                    nextStepLinkUrl: `/keyword-project/${props.projectId}/titles/${keywordHash}`,
                                }
                            );
                            setTimeout(() => {
                                props.successAlertRef.current?.close();
                            }, 5000);

                            // Cleanup after success alert is shown
                            setTimeout(() => {
                                cleanupTaskProgress(keywordHash);  // Remove progress and message after completion
                            }, 3000);  // Cleanup after 3 seconds                            
                        }, 1000 * 3);
                    }
                }
            });
        }, 2000);
    };

    const pollIcpTaskProgress = (taskId, projectId) => {
        const interval = setInterval(() => {
            getTaskProgress(taskId)
                .then((res) => {
                    const status = res.data.status;
                    if (status === "success") {
                        clearInterval(interval);
                        // setTimeout(() => successAlertRef.current?.close(), 5000);
                        tableRef.current?.refetchData()
                        fetchKwTitles()
                        SetIsIcpLoading(false);
                        localStorage.removeItem(`task_${projectId}`);
                    } else if (status === "failure") {
                        clearInterval(interval);
                        props.failAlertRef.current?.show("Task failed. Please try again.");
                        setTimeout(() => props.failAlertRef.current?.close(), 5000);
                        SetIsIcpLoading(false)
                    }
                })
                .catch((err) => {
                    console.error("Error fetching task progress:", err);
                    clearInterval(interval);
                    props.failAlertRef.current?.show("Error fetching task progress.");
                    setTimeout(() => props.failAlertRef.current?.close(), 5000);
                    SetIsIcpLoading(false)
                });
        }, 2000);
    };

    const cleanupTaskProgress = (keywordHash: string) => {
        // Cleanup progress and message for this keyword
        setTitleGenProgressMap(prev => {
            const newState = { ...prev };
            delete newState[keywordHash];
            return newState;
        });
        setTitleGenProgressMessageMap((prev: Record<string, string>) => {
            const newState = { ...prev };
            delete newState[keywordHash];
            return newState;
        });

        // Clear the interval associated with this task
        if (taskPollingIntervals.current[keywordHash]) {
            clearInterval(taskPollingIntervals.current[keywordHash]);
            delete taskPollingIntervals.current[keywordHash];
        }

        // Remove task ID from localStorage
        localStorage.removeItem(`titleGenTask-${keywordHash}`);
    }

    const openUrlInNewTab = (url: string) => {
        window.open(url, "_blank");
    }

    const handleDelete = (keywordHash: string, keyword: string, difficultyScore: number) => {
        setDeleteData({ keywordHash, keyword, difficultyScore });
        setShowDeletePopUp(true);
    }

    function selectedRowsSetter(rowModel: RowModel<unknown>) {
        const rows = rowModel.rows as Row<KeywordsProjectTableProps>[];
        const selectedData: KeywordsProjectTableProps[] = rows.map(row => row.original);
        setSelectedRows(selectedData);
    }



    if (isIcpLoading) {
        return (<>
            <div className="w-100">
                <div className={""} style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                }}
                >
                    <AbunLoader show={isIcpLoading} height="50vh" />
                </div>
            </div>
        </>
        )
    } else {
        return (
            <>
                <div className={"keyword-project-content"}>
                    <h1>Choose Keyword → Generate Title → Create Article</h1>
                    <div className='keyword-project-infos'>
                        <div className='keyword-project-info'>
                            <span> {totalKeywords} Total Keywords </span>
                            <span className='dot-separator'> | </span>
                            <span> Total Keyword Traffic: {totalTrafficVolume}
                                <img
                                    loading="lazy"
                                    width="20"
                                    style={props.locationIsoCode === "zz" ? { position: "absolute" } : {}}
                                    srcSet={props.locationIsoCode !== "zz" ? `https://flagcdn.com/32x24/${props.locationIsoCode.toLowerCase()}.png 2x` : "https://img.icons8.com/?size=100&id=3685"}
                                    src={props.locationIsoCode !== "zz" ? `https://flagcdn.com/16x12/${props.locationIsoCode.toLowerCase()}.png` : "https://img.icons8.com/?size=100&id=3685"}
                                    alt={props.locationIsoCode}
                                />
                            </span>
                        </div>
                        <div className='keyword-project-additional-info'>
                            <span className='very-easy'>
                                <b>{total_very_easy_keywords}</b> Very Easy Keywords
                            </span>
                            <span className='dot-separator'> | </span>
                            <span className='easy'>
                                <b>{total_easy_keywords}</b> Easy Keywords
                            </span>
                            <span className='dot-separator'> | </span>
                            <span className='moderate'>
                                <b>{total_moderate_keywords}</b> Moderate Keywords
                            </span>
                            <span className='dot-separator'> | </span>
                            <span className='hard'>
                                <b>{total_hard_keywords}</b> Hard Keywords
                            </span>
                            <span className='dot-separator'> | </span>
                            <span className='very-hard'>
                                <b>{total_very_hard_keywords}</b> Very Hard Keywords
                            </span>
                        </div>
                    </div>

                    <div className={`table-container ${props.icpKeywordProject ? 'icp-keyword has-checkbox-header' : ''}`} style={props.icpKeywordProject ? { width: '100%', position: 'relative', overflow: "visible" } : {}}>
                        {
                            isFetching ?
                                <div className={"loadingDataCard card mt-4"} style={{ width: "100%" }}>
                                    <div className={"card-content"}>
                                        <div className={"content is-flex is-justify-content-center"}>
                                            <p style={{ textAlign: "center", fontSize: "1.3rem" }}>
                                                Loading Data...<Icon iconName={"spinner"} marginClass={"ml-5"} />
                                            </p>
                                        </div>
                                    </div>
                                </div> :
                                <>
                                    {props.icpKeywordProject ? (
                                        <div className={"location-input"}>
                                            <div className='is-flex is-flex-direction-row'>
                                                <AbunButton type={"primary"}
                                                    className={"is-small comp-research-table-button"}
                                                    style={{ top: "18px", right: kwVolumeCount < 10 ? "10px" : kwVolumeCount < 1000 ? "6px" : "2px" }}
                                                    disabled={kwVolumeCount === 0}
                                                    clickHandler={() => {
                                                        const keywordHashIds = tableData
                                                            .filter(row => !row.kwVolume)
                                                            .map(row => row.keywordHash);

                                                        handleFetchVolume(keywordHashIds, selectedLocation);
                                                    }}>
                                                    Get Search Volume for All Keywords {kwVolumeCount ?? 0}
                                                </AbunButton>
                                                <Autocomplete
                                                    id="manual-keywords-location-select-autocomplete"
                                                    sx={{ width: 200 }}
                                                    disablePortal={false} //
                                                    options={countries}
                                                    value={selectedLocation}
                                                    autoHighlight
                                                    componentsProps={{
                                                        popper: {
                                                            modifiers: [
                                                                {
                                                                    name: 'zIndex',
                                                                    enabled: true,
                                                                    phase: 'write',
                                                                    fn({ state }: any) {
                                                                        state.styles.popper.zIndex = '1500';
                                                                    }
                                                                }
                                                            ]
                                                        }
                                                    }}
                                                    getOptionLabel={(option) => option.country_iso_code !== "ZZ" ? `${option.location_name} (${option.country_iso_code})` : option.location_name}
                                                    isOptionEqualToValue={(option, value) => option.location_code === value.location_code}
                                                    renderOption={(props, option) => (
                                                        <Box component="li" sx={{ '& > img': { mr: 2, flexShrink: 0 } }} {...props}>
                                                            <img
                                                                loading="lazy"
                                                                width="20"
                                                                srcSet={option.country_iso_code !== "ZZ" ? `https://flagcdn.com/w40/${option.country_iso_code.toLowerCase()}.png 2x` : EarthFlag}
                                                                src={option.country_iso_code !== "ZZ" ? `https://flagcdn.com/w20/${option.country_iso_code.toLowerCase()}.png` : EarthFlag}
                                                                alt=""
                                                            />
                                                            {option.location_name} ({option.country_iso_code})
                                                        </Box>
                                                    )}
                                                    renderInput={(params) => (
                                                        <TextField
                                                            {...params}
                                                            label="Location"
                                                            inputProps={{
                                                                ...params.inputProps,
                                                                autoComplete: 'off',
                                                            }}
                                                        />
                                                    )}
                                                    onChange={(_event, option) => {
                                                        if (option) {
                                                            setSelectedLocation(option);
                                                        }
                                                    }}
                                                />
                                            </div>
                                        </div>)
                                        : <></>
                                    }
                                    <KeywordProjectTable
                                        tableRef={tableRef}
                                        tableData={tableData}
                                        pageSizes={pageSizes}
                                        selectedTab={selectedTab}
                                        selectedTabInfo={selectedTabInfo}
                                        setSelectedTab={setSelectedTab}
                                        setSelectedTabInfo={setSelectedTabInfo}
                                        isFetching={isFetching}
                                        titleGenProgressMap={titleGenProgressMap}
                                        titleGenProgressMessageMap={titleGenProgressMessageMap}
                                        setTitleGenProgressMap={setTitleGenProgressMap}
                                        setTitleGenProgressMessageMap={setTitleGenProgressMessageMap}
                                        total_very_easy_keywords={total_very_easy_keywords}
                                        total_easy_keywords={total_easy_keywords}
                                        total_moderate_keywords={total_moderate_keywords}
                                        total_hard_keywords={total_hard_keywords}
                                        total_very_hard_keywords={total_very_hard_keywords}
                                        projectId={props.projectId}
                                        selectedRowsSetter={props.icpKeywordProject ? selectedRowsSetter : undefined}
                                        selectedRows={props.icpKeywordProject ? selectedRows : []}
                                        icpKeywordProject={props.icpKeywordProject}
                                        handleFetchVolume={handleFetchVolume}
                                        selectedLocation={selectedLocation}
                                        generatingVolume={generatingVolume}
                                        availableBulkActions={availableBulkActions}
                                        bulkActionsEnabled={bulkActionsEnabled}
                                        setShowCreateCustomkeywordModal={setShowCreateCustomkeywordModal}
                                        handleDelete={handleDelete}
                                        startTitleGenTask={startTitleGenTask}
                                        openUrlInNewTab={openUrlInNewTab}
                                        failAlertRef={props.failAlertRef}
                                    />
                                </>
                        }

                        {/* Modals */}
                        <CreateCustomKeywordModal
                            showCreateCustomkeywordModal={showCreateCustomkeywordModal}
                            customKeyword={customKeyword}
                            setCustomKeyword={setCustomKeyword}
                            setShowCreateCustomkeywordModal={setShowCreateCustomkeywordModal}
                            createCustomKeyword={createCustomKeyword}
                            projectId={props.projectId}
                            refetch={refetch}
                            successAlertRef={props.successAlertRef}
                            failAlertRef={props.failAlertRef}
                        />

                        <DeleteKeywordModal
                            showDeletePopUp={showDeletePopUp}
                            deleteData={deleteData}
                            setShowDeletePopUp={setShowDeletePopUp}
                            removeKeyword={removeKeyword}
                            refetch={refetch}
                            successAlertRef={props.successAlertRef}
                            failAlertRef={props.failAlertRef}
                            setTotalKeywords={setTotalKeywords}
                            setTotalVeryEasyKeywords={setTotalVeryEasyKeywords}
                            setTotalEasyKeywords={setTotalEasyKeywords}
                            setTotalModerateKeywords={setTotalModerateKeywords}
                            setTotalHardKeywords={setTotalHardKeywords}
                            setTotalVeryHardKeywords={setTotalVeryHardKeywords}
                        />
                    </div>
                </div>
            </>
        );
    }
}
