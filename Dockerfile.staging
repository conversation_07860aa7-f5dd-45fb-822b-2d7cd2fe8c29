# Make sure to build from project's root directory
# All paths are considered from project's root (because of above comment)

FROM node:20.12-alpine

# Add build env variables
ARG REACT_APP_DRF_DOMAIN=https://staging.api.abun.com
ARG REACT_APP_STRIPE_CHECKOUT_SUCCESS_URL=https://staging.app.abun.com/checkout/success?session_id={CHECKOUT_SESSION_ID}
ARG REACT_APP_STRIPE_CHECKOUT_CANCEL_URL=https://staging.app.abun.com/show-articles
# This is a Stripe publishable key intended for public use
ARG REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_test_51NhU8USAw4DIVBDnjAl8kHmSezcNLzli0XZ2jbgpszj36p6Cek5Fcrp1sCKikeSsMTlbas3LoWhsnPyicTFuxjDQ00dTPvbjoI
ARG REACT_APP_LOGO_URL=https://static.abun.com/staging/logo

# Copy package.json and lock files & install node modules and "serve" library (https://github.com/vercel/serve)
COPY package.json package-lock.json ./
RUN npm install && npm install -g serve

# Copy remaining source code (certain files and folders will be excluded by .dockerignore)
COPY . .

# Build production files
RUN npm run build

# Expose port 300
EXPOSE 3000

# Run server
ENTRYPOINT ["serve", "-s", "build", "-l", "3000"]
